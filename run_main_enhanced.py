#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل البرنامج الرئيسي المحسن لإدارة البيانات المالية
هذا هو البرنامج الرئيسي الذي يحتوي على جميع الخصائص المحسنة
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """
    التحقق من وجود المكتبات المطلوبة
    """
    required_packages = ['pandas', 'openpyxl', 'tkinter']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        error_msg = f"""
❌ المكتبات التالية مفقودة:
{', '.join(missing_packages)}

📦 لتثبيت المكتبات المطلوبة، قم بتشغيل الأمر التالي:
pip install {' '.join([pkg for pkg in missing_packages if pkg != 'tkinter'])}

ملاحظة: tkinter مدمج مع Python عادة
        """
        
        print(error_msg)
        
        # إنشاء نافذة خطأ بسيطة
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("مكتبات مفقودة", error_msg)
            root.destroy()
        except:
            pass
        
        return False
    
    return True

def main():
    """
    الدالة الرئيسية لتشغيل البرنامج المحسن
    """
    print("=" * 70)
    print("🚀 برنامج إدارة البيانات المالية للطلاب - النسخة المحسنة")
    print("=" * 70)
    print("✨ الميزات المحسنة:")
    print("   📋 قراءة رؤوس الأعمدة الفعلية من ملفات Excel")
    print("   🔢 عرض الأصفار بدلاً من 'غير محدد' في الأعمدة المالية")
    print("   🎨 رؤوس أعمدة ملونة وتفاعلية")
    print("   🔍 فلاتر بحث متقدمة ديناميكية")
    print("   📊 إحصائيات سريعة محدثة")
    print("   🔄 فرز ديناميكي للأعمدة")
    print("   📱 شريط جانبي قابل للطي")
    print("   🎯 تمرير أفقي وعمودي محسن")
    print("   💰 معالجة متقدمة للبيانات المالية")
    print("   🔧 أعمدة ديناميكية تتكيف مع البيانات")
    print("=" * 70)
    
    # التحقق من المكتبات المطلوبة
    print("🔍 جاري التحقق من المكتبات المطلوبة...")
    if not check_dependencies():
        print("❌ فشل في التحقق من المكتبات")
        input("اضغط Enter للخروج...")
        return
    
    print("✅ جميع المكتبات متوفرة")
    
    # تهيئة قاعدة البيانات
    print("🗄️ جاري تهيئة قاعدة البيانات...")
    try:
        from database_manager import DatabaseManager
        from config import DATABASE_NAME
        
        db_manager = DatabaseManager()
        print(f"✅ تم إنشاء قاعدة البيانات: {DATABASE_NAME}")
        
    except Exception as e:
        error_msg = f"❌ خطأ في تهيئة قاعدة البيانات: {str(e)}"
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
            root.destroy()
        except:
            pass
        
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل الواجهة المحسنة
    print("🎨 جاري تشغيل الواجهة المحسنة...")
    
    try:
        from enhanced_gui import run_enhanced_gui
        
        print("✅ تم تشغيل البرنامج المحسن بنجاح")
        print("=" * 70)
        print("📝 تعليمات الاستخدام:")
        print("   1. انقر على 'استيراد Excel' لتحميل بيانات الطلاب")
        print("   2. ستظهر رؤوس الأعمدة الفعلية من ملف Excel")
        print("   3. القيم الفارغة في الأعمدة المالية ستظهر كأصفار")
        print("   4. استخدم فلاتر البحث للعثور على بيانات محددة")
        print("   5. انقر على رؤوس الأعمدة للفرز")
        print("=" * 70)
        
        # تشغيل الواجهة
        run_enhanced_gui()
        
    except Exception as e:
        error_msg = f"❌ خطأ في تشغيل الواجهة المحسنة: {str(e)}"
        print(f"❌ {error_msg}")
        print("\n📋 تفاصيل الخطأ:")
        traceback.print_exc()
        
        print("\n🔄 جاري المحاولة مع الواجهة العادية...")
        
        # العودة للواجهة العادية في حالة الخطأ
        try:
            from gui_interface import FinanceGUI
            app = FinanceGUI()
            app.run()
        except Exception as e2:
            error_msg2 = f"❌ خطأ في تشغيل الواجهة العادية أيضاً: {str(e2)}"
            print(error_msg2)
            
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("خطأ في التشغيل", f"{error_msg}\n\n{error_msg2}")
                root.destroy()
            except:
                pass
            
            input("اضغط Enter للخروج...")

def show_help():
    """
    عرض معلومات المساعدة
    """
    help_text = """
🚀 برنامج إدارة البيانات المالية للطلاب - النسخة المحسنة
================================================================

📋 الاستخدام:
    python run_main_enhanced.py        - تشغيل البرنامج المحسن
    python run_main_enhanced.py --help - عرض هذه المساعدة

✨ الميزات الجديدة في النسخة المحسنة:
    📋 قراءة رؤوس الأعمدة الفعلية من ملفات Excel
    🔢 عرض الأصفار بدلاً من 'غير محدد' في الأعمدة المالية
    🎨 رؤوس أعمدة ملونة وتفاعلية
    🔍 فلاتر بحث متقدمة ديناميكية
    📊 إحصائيات سريعة محدثة
    🔄 فرز ديناميكي للأعمدة
    📱 شريط جانبي قابل للطي
    🎯 تمرير أفقي وعمودي محسن
    💰 معالجة متقدمة للبيانات المالية
    🔧 أعمدة ديناميكية تتكيف مع البيانات

🔧 المتطلبات:
    • Python 3.6 أو أحدث
    • pandas
    • openpyxl
    • tkinter (مدمج مع Python)

📚 للحصول على المساعدة:
    راجع ملف requirements_documentation.md
    """
    print(help_text)

if __name__ == "__main__":
    # التحقق من معاملات سطر الأوامر
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['--version', '-v']:
            print("برنامج إدارة البيانات المالية للطلاب - النسخة المحسنة 2.0")
            sys.exit(0)
        else:
            print(f"❌ معامل غير معروف: {sys.argv[1]}")
            print("استخدم --help للحصول على المساعدة")
            sys.exit(1)
    
    # تشغيل البرنامج
    main()
