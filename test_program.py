#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لبرنامج إدارة البيانات المالية
"""

import sys
import os
import pandas as pd
from database_manager import DatabaseManager
from data_processor import DataProcessor
from excel_handler import ExcelHandler

def test_database():
    """
    اختبار قاعدة البيانات
    """
    print("🔍 اختبار قاعدة البيانات...")
    
    try:
        db = DatabaseManager()
        
        # اختبار إنشاء قاعدة البيانات
        stats = db.get_statistics()
        print(f"✓ قاعدة البيانات تعمل - عدد الطلاب: {stats['total_students']}")
        
        # اختبار هيكل الرسوم
        fees = db.get_fee_structure()
        print(f"✓ هيكل الرسوم محمل - عدد المراحل: {len(fees)}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في قاعدة البيانات: {e}")
        return False

def test_excel_handler():
    """
    اختبار معالج Excel
    """
    print("\n📊 اختبار معالج Excel...")
    
    try:
        excel_handler = ExcelHandler()
        
        # اختبار قراءة ملف البيانات المثال
        if os.path.exists("sample_students_data.xlsx"):
            df, errors = excel_handler.import_excel_file("sample_students_data.xlsx")
            
            if errors:
                print(f"⚠️ تحذيرات في قراءة الملف: {errors}")
            else:
                print(f"✓ تم قراءة ملف Excel - عدد الصفوف: {len(df)}")
                print(f"✓ عدد الأعمدة: {len(df.columns)}")
                
            return True
        else:
            print("⚠️ ملف البيانات المثال غير موجود")
            return True
            
    except Exception as e:
        print(f"✗ خطأ في معالج Excel: {e}")
        return False

def test_data_processor():
    """
    اختبار معالج البيانات
    """
    print("\n⚙️ اختبار معالج البيانات...")
    
    try:
        db = DatabaseManager()
        processor = DataProcessor(db)
        
        # إنشاء بيانات اختبار بسيطة
        test_data = pd.DataFrame({
            'اسم الطالب': ['أحمد محمد', 'فاطمة علي'],
            'المرحلة': ['الابتدائية', 'الثانوية'],
            'مديونية سابقة 23/24': [1000, 2000],
            'القسط الأول': [2000, 3000],
            'القسط الثاني': [1500, 2500],
            'خصم إخوة': [500, 0]
        })
        
        # اختبار تخطيط الأعمدة
        mapped_df = processor.map_columns(test_data)
        print(f"✓ تخطيط الأعمدة - عدد الأعمدة المخططة: {len(mapped_df.columns)}")
        
        # اختبار تنظيف البيانات
        cleaned_df = processor.clean_data(mapped_df)
        print("✓ تنظيف البيانات")
        
        # اختبار حساب البيانات المالية
        calculated_df = processor.calculate_financial_data(cleaned_df)
        print("✓ حساب البيانات المالية")
        
        # اختبار التحقق من صحة البيانات
        is_valid, errors = processor.validate_data(calculated_df)
        if is_valid:
            print("✓ التحقق من صحة البيانات")
        else:
            print(f"⚠️ تحذيرات في البيانات: {errors}")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في معالج البيانات: {e}")
        return False

def test_full_workflow():
    """
    اختبار سير العمل الكامل
    """
    print("\n🔄 اختبار سير العمل الكامل...")
    
    try:
        # تهيئة المكونات
        db = DatabaseManager()
        processor = DataProcessor(db)
        excel_handler = ExcelHandler()
        
        # قراءة ملف البيانات المثال إذا كان موجوداً
        if os.path.exists("sample_students_data.xlsx"):
            print("📖 قراءة ملف البيانات المثال...")
            df, errors = excel_handler.import_excel_file("sample_students_data.xlsx")
            
            if not errors and not df.empty:
                print("⚙️ معالجة البيانات...")
                processed_df, processing_errors = processor.process_excel_data(df)
                
                if not processing_errors:
                    print("💾 تحضير البيانات لقاعدة البيانات...")
                    records = processor.prepare_for_database(processed_df)
                    
                    print(f"✓ سير العمل الكامل - تم تحضير {len(records)} سجل")
                    return True
                else:
                    print(f"⚠️ تحذيرات في المعالجة: {processing_errors}")
                    return True
            else:
                print(f"⚠️ مشاكل في قراءة الملف: {errors}")
                return False
        else:
            print("⚠️ ملف البيانات المثال غير موجود - تخطي اختبار سير العمل")
            return True
            
    except Exception as e:
        print(f"✗ خطأ في سير العمل: {e}")
        return False

def main():
    """
    تشغيل جميع الاختبارات
    """
    print("=" * 60)
    print("🧪 اختبار شامل لبرنامج إدارة البيانات المالية للطلاب")
    print("=" * 60)
    
    tests = [
        ("قاعدة البيانات", test_database),
        ("معالج Excel", test_excel_handler),
        ("معالج البيانات", test_data_processor),
        ("سير العمل الكامل", test_full_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: نجح")
        else:
            print(f"❌ {test_name}: فشل")
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبار نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت - راجع الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
