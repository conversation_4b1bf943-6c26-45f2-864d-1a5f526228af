#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مالي مبسط وفعال - يحل مشكلة الأصفار والأزرار
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import os
from typing import Optional

class SimpleFinanceApp:
    def __init__(self):
        """
        تهيئة التطبيق المبسط
        """
        self.root = tk.Tk()
        self.root.title("برنامج البيانات المالية - النسخة المبسطة والفعالة")

        # تحديد حجم النافذة لتشغل 90% من الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = int(screen_width * 0.9)
        window_height = int(screen_height * 0.9)

        # توسيط النافذة
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # البيانات
        self.current_data = pd.DataFrame()

        # إعداد الأنماط
        self.setup_styles()

        # إعداد الواجهة
        self.setup_ui()

    def setup_styles(self):
        """
        إعداد الأنماط المحسنة
        """
        style = ttk.Style()
        style.theme_use('clam')

        # نمط رؤوس الأعمدة - مع إصلاح مشكلة الهوفر
        style.configure(
            "Treeview.Heading",
            background='#4a90e2',
            foreground='white',
            font=('Arial', 12, 'bold'),
            relief='raised',
            borderwidth=1,
            focuscolor='none'
        )

        # إصلاح مشكلة الهوفر
        style.map("Treeview.Heading",
                 background=[('active', '#3a7bc8'),
                           ('pressed', '#2a6bb8')],
                 foreground=[('active', 'white'),
                           ('pressed', 'white'),
                           ('!active', 'white')])

        # نمط الجدول
        style.configure(
            "Treeview",
            background='white',
            foreground='black',
            rowheight=30,
            fieldbackground='white',
            font=('Arial', 11)
        )

        # نمط الأزرار
        style.configure(
            "TButton",
            font=('Arial', 11)
        )

        style.configure(
            "Accent.TButton",
            background='#0078d4',
            foreground='white',
            font=('Arial', 11, 'bold')
        )

    def setup_ui(self):
        """
        إعداد واجهة المستخدم
        """
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط الأدوات
        self.create_toolbar(main_frame)

        # منطقة عرض البيانات
        self.create_data_display(main_frame)

        # شريط الحالة
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """
        إنشاء شريط الأدوات
        """
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # الأزرار الأساسية
        ttk.Button(toolbar_frame, text="📁 استيراد Excel",
                  command=self.import_excel, width=20,
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar_frame, text="💾 تصدير Excel",
                  command=self.export_excel, width=20).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar_frame, text="📋 إنشاء قالب",
                  command=self.create_template, width=20).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar_frame, text="🚀 نقل للسنة الجديدة",
                  command=self.transfer_to_new_year, width=20,
                  style='Accent.TButton').pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(toolbar_frame, text="🔄 تحديث",
                  command=self.refresh_data, width=15).pack(side=tk.RIGHT)

        # معلومات الملف
        self.file_info_label = ttk.Label(toolbar_frame, text="لم يتم تحميل ملف",
                                        font=('Arial', 10, 'italic'))
        self.file_info_label.pack(side=tk.RIGHT, padx=(0, 20))

    def create_data_display(self, parent):
        """
        إنشاء منطقة عرض البيانات
        """
        # إطار الجدول
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview
        self.tree = ttk.Treeview(table_frame, show='headings', height=20)

        # أشرطة التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)

        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # ترتيب العناصر
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين الشبكة
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)

    def create_status_bar(self, parent):
        """
        إنشاء شريط الحالة
        """
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_label = ttk.Label(status_frame, text="جاهز",
                                     relief=tk.SUNKEN, anchor=tk.W, font=('Arial', 10))
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.count_label = ttk.Label(status_frame, text="عدد الصفوف: 0",
                                    relief=tk.SUNKEN, font=('Arial', 10))
        self.count_label.pack(side=tk.RIGHT, padx=(10, 0))

    def import_excel(self):
        """
        استيراد ملف Excel - مبسط وفعال
        """
        try:
            self.update_status("جاري اختيار الملف...")

            # اختيار الملف
            file_path = filedialog.askopenfilename(
                title="اختر ملف البيانات المالية",
                filetypes=[
                    ("Excel files", "*.xlsx *.xls"),
                    ("CSV files", "*.csv"),
                    ("All files", "*.*")
                ]
            )

            if not file_path:
                self.update_status("لم يتم اختيار ملف")
                return

            self.update_status("جاري قراءة الملف...")

            # قراءة الملف
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path, engine='openpyxl')

            if df.empty:
                messagebox.showwarning("تحذير", "الملف فارغ")
                self.update_status("الملف فارغ")
                return

            # معالجة البيانات
            self.update_status("جاري معالجة البيانات...")

            # تنظيف البيانات - استبدال القيم الفارغة بـ "غير محدد"
            for col in df.columns:
                if df[col].dtype == 'object':  # أعمدة نصية
                    df[col] = df[col].fillna('غير محدد')
                else:  # أعمدة رقمية
                    df[col] = df[col].fillna('غير محدد')

            # حفظ البيانات
            self.current_data = df

            # عرض البيانات
            self.display_data()

            # تحديث معلومات الملف
            file_name = os.path.basename(file_path)
            self.file_info_label.config(text=f"الملف: {file_name}")

            # رسالة النجاح
            messagebox.showinfo("نجح الاستيراد",
                              f"تم استيراد {len(df)} صف و {len(df.columns)} عمود بنجاح!\n\n"
                              f"الملف: {file_name}")

            self.update_status(f"تم استيراد {len(df)} صف بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد الملف:\n{str(e)}")
            self.update_status("خطأ في الاستيراد")

    def display_data(self):
        """
        عرض البيانات في الجدول
        """
        if self.current_data.empty:
            return

        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        # تحديد الأعمدة
        columns = list(self.current_data.columns)
        self.tree['columns'] = columns

        # تعيين رؤوس الأعمدة
        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER)
            self.tree.column(col, width=150, anchor=tk.CENTER)

        # إضافة البيانات
        for idx, row in self.current_data.iterrows():
            values = []
            for col in columns:
                value = row[col]
                if pd.isna(value):
                    values.append('غير محدد')
                else:
                    values.append(str(value))

            # ألوان متناوبة
            tag = 'evenrow' if idx % 2 == 0 else 'oddrow'
            self.tree.insert('', tk.END, values=values, tags=(tag,))

        # تطبيق الألوان
        self.tree.tag_configure('evenrow', background='white')
        self.tree.tag_configure('oddrow', background='#f0f0f0')

        # تحديث العداد
        self.count_label.config(text=f"عدد الصفوف: {len(self.current_data)}")

    def export_excel(self):
        """
        تصدير البيانات إلى Excel
        """
        try:
            if self.current_data.empty:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if not file_path:
                return

            self.update_status("جاري تصدير البيانات...")

            # حفظ الملف
            self.current_data.to_excel(file_path, index=False, engine='openpyxl')

            messagebox.showinfo("نجح التصدير", f"تم حفظ الملف بنجاح:\n{file_path}")
            self.update_status("تم التصدير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف:\n{str(e)}")
            self.update_status("خطأ في التصدير")

    def create_template(self):
        """
        إنشاء قالب Excel
        """
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ قالب Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if not file_path:
                return

            # إنشاء قالب بالأعمدة الأساسية
            template_columns = [
                'اسم الطالب', 'المرحلة', 'مديونية سابقة 23/24',
                'مصروفات عام 2023/2024', 'مصروفات دراسية 2025',
                'القسط الأول', 'القسط الثاني', 'القسط الثالث',
                'خصم إخوة', 'خصم كاش', 'ملاحظات'
            ]

            template_df = pd.DataFrame(columns=template_columns)

            # إضافة صف مثال
            example_row = {
                'اسم الطالب': 'مثال على اسم الطالب',
                'المرحلة': 'الابتدائية',
                'مديونية سابقة 23/24': 1000,
                'مصروفات عام 2023/2024': 15000,
                'مصروفات دراسية 2025': 18000,
                'القسط الأول': 2000,
                'القسط الثاني': 2000,
                'القسط الثالث': 0,
                'خصم إخوة': 500,
                'خصم كاش': 0,
                'ملاحظات': 'مثال على الملاحظات'
            }

            template_df = pd.concat([template_df, pd.DataFrame([example_row])], ignore_index=True)

            # حفظ القالب
            template_df.to_excel(file_path, index=False, engine='openpyxl')

            messagebox.showinfo("نجح إنشاء القالب", f"تم إنشاء القالب بنجاح:\n{file_path}")
            self.update_status("تم إنشاء القالب بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء القالب:\n{str(e)}")
            self.update_status("خطأ في إنشاء القالب")

    def transfer_to_new_year(self):
        """
        نقل البيانات للسنة المالية الجديدة - الهدف الأساسي للبرنامج
        """
        try:
            if self.current_data.empty:
                messagebox.showwarning("تحذير", "يجب استيراد بيانات السنة المالية القديمة أولاً")
                return

            # نافذة إعدادات النقل
            transfer_window = tk.Toplevel(self.root)
            transfer_window.title("نقل البيانات للسنة المالية الجديدة 2025")
            transfer_window.geometry("600x500")
            transfer_window.transient(self.root)
            transfer_window.grab_set()

            # إطار الإعدادات
            settings_frame = ttk.LabelFrame(transfer_window, text="إعدادات النقل", padding=10)
            settings_frame.pack(fill=tk.X, padx=10, pady=10)

            # رسوم السنة الجديدة
            ttk.Label(settings_frame, text="رسوم السنة الجديدة 2025:", font=('Arial', 12, 'bold')).pack(anchor=tk.W)

            fees_frame = ttk.Frame(settings_frame)
            fees_frame.pack(fill=tk.X, pady=5)

            # متغيرات الرسوم
            self.new_fees = {}

            fee_types = [
                ('الابتدائية', 18000),
                ('الإعدادية', 20000),
                ('الثانوية', 25000)
            ]

            for i, (stage, default_fee) in enumerate(fee_types):
                row_frame = ttk.Frame(fees_frame)
                row_frame.pack(fill=tk.X, pady=2)

                ttk.Label(row_frame, text=f"{stage}:", width=15).pack(side=tk.LEFT)

                fee_var = tk.StringVar(value=str(default_fee))
                self.new_fees[stage] = fee_var

                ttk.Entry(row_frame, textvariable=fee_var, width=15).pack(side=tk.LEFT, padx=5)
                ttk.Label(row_frame, text="جنيه").pack(side=tk.LEFT)

            # خيارات النقل
            options_frame = ttk.LabelFrame(transfer_window, text="خيارات النقل", padding=10)
            options_frame.pack(fill=tk.X, padx=10, pady=5)

            self.transfer_debt = tk.BooleanVar(value=True)
            ttk.Checkbutton(options_frame, text="نقل المديونيات السابقة",
                           variable=self.transfer_debt).pack(anchor=tk.W)

            self.reset_payments = tk.BooleanVar(value=True)
            ttk.Checkbutton(options_frame, text="إعادة تعيين الأقساط المدفوعة",
                           variable=self.reset_payments).pack(anchor=tk.W)

            self.keep_discounts = tk.BooleanVar(value=True)
            ttk.Checkbutton(options_frame, text="الاحتفاظ بالخصومات",
                           variable=self.keep_discounts).pack(anchor=tk.W)

            # معاينة النقل
            preview_frame = ttk.LabelFrame(transfer_window, text="معاينة النقل", padding=10)
            preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            preview_text = tk.Text(preview_frame, height=8, font=('Arial', 10))
            preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=preview_text.yview)
            preview_text.configure(yscrollcommand=preview_scrollbar.set)

            preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # تحديث المعاينة
            def update_preview():
                preview_text.delete(1.0, tk.END)

                preview_info = f"📊 إحصائيات النقل:\n"
                preview_info += f"   👥 عدد الطلاب: {len(self.current_data)}\n\n"

                for stage in ['الابتدائية', 'الإعدادية', 'الثانوية']:
                    stage_count = len(self.current_data[self.current_data['المرحلة'] == stage])
                    if stage_count > 0:
                        new_fee = self.new_fees[stage].get()
                        preview_info += f"🎓 {stage}: {stage_count} طالب - رسوم جديدة: {new_fee} جنيه\n"

                if self.transfer_debt.get():
                    total_debt = self.current_data.get('مديونية سابقة 23/24', pd.Series([0])).fillna(0).sum()
                    preview_info += f"\n💰 إجمالي المديونيات المنقولة: {total_debt:,.0f} جنيه\n"

                preview_info += f"\n✅ سيتم إنشاء ملف جديد للسنة المالية 2025"

                preview_text.insert(1.0, preview_info)

            # أزرار التحكم
            buttons_frame = ttk.Frame(transfer_window)
            buttons_frame.pack(fill=tk.X, padx=10, pady=10)

            ttk.Button(buttons_frame, text="🔄 تحديث المعاينة",
                      command=update_preview).pack(side=tk.LEFT, padx=5)

            ttk.Button(buttons_frame, text="🚀 تنفيذ النقل",
                      command=lambda: self.execute_transfer(transfer_window),
                      style='Accent.TButton').pack(side=tk.RIGHT, padx=5)

            ttk.Button(buttons_frame, text="❌ إلغاء",
                      command=transfer_window.destroy).pack(side=tk.RIGHT)

            # تحديث المعاينة الأولي
            update_preview()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح نافذة النقل: {str(e)}")

    def execute_transfer(self, transfer_window):
        """
        تنفيذ عملية النقل
        """
        try:
            self.update_status("جاري تنفيذ النقل للسنة الجديدة...")

            # إنشاء البيانات الجديدة
            new_data = self.current_data.copy()

            # تحديث الرسوم حسب المرحلة
            for stage, fee_var in self.new_fees.items():
                new_fee = float(fee_var.get())
                mask = new_data['المرحلة'] == stage
                new_data.loc[mask, 'مصروفات دراسية 2025'] = new_fee

            # معالجة المديونيات
            if self.transfer_debt.get():
                # حساب المديونية الجديدة = المديونية السابقة + المتبقي من السنة الحالية
                old_debt = new_data.get('مديونية سابقة 23/24', pd.Series([0])).fillna(0)

                # حساب المتبقي (إذا كان موجود)
                if 'المتبقي' in new_data.columns:
                    remaining = new_data['المتبقي'].fillna(0)
                else:
                    remaining = pd.Series([0] * len(new_data))

                new_data['مديونية سابقة 24/25'] = old_debt + remaining
            else:
                new_data['مديونية سابقة 24/25'] = 0

            # إعادة تعيين الأقساط
            if self.reset_payments.get():
                payment_columns = ['القسط الأول', 'القسط الثاني', 'القسط الثالث']
                for col in payment_columns:
                    if col in new_data.columns:
                        new_data[col] = 0

            # معالجة الخصومات
            if not self.keep_discounts.get():
                discount_columns = ['خصم إخوة', 'خصم كاش']
                for col in discount_columns:
                    if col in new_data.columns:
                        new_data[col] = 0

            # تحديث السنة في الأعمدة
            new_data = new_data.rename(columns={
                'مديونية سابقة 23/24': 'مديونية سابقة 24/25',
                'مصروفات عام 2023/2024': 'مصروفات عام 2024/2025'
            })

            # إضافة عمود السنة الجديدة
            new_data['السنة المالية'] = '2025'

            # حفظ الملف الجديد
            new_filename = f"البيانات_المالية_2025_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            new_data.to_excel(new_filename, index=False, engine='openpyxl')

            # تحديث البيانات الحالية
            self.current_data = new_data
            self.display_data()

            # إغلاق نافذة النقل
            transfer_window.destroy()

            # رسالة النجاح
            success_msg = f"🎉 تم نقل البيانات للسنة المالية الجديدة بنجاح!\n\n"
            success_msg += f"📁 الملف الجديد: {new_filename}\n"
            success_msg += f"👥 عدد الطلاب المنقولين: {len(new_data)}\n"

            if self.transfer_debt.get():
                total_debt = new_data['مديونية سابقة 24/25'].sum()
                success_msg += f"💰 إجمالي المديونيات المنقولة: {total_debt:,.0f} جنيه\n"

            messagebox.showinfo("نجح النقل", success_msg)
            self.update_status(f"تم النقل بنجاح - {len(new_data)} طالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تنفيذ النقل: {str(e)}")
            self.update_status("خطأ في النقل")

    def refresh_data(self):
        """
        تحديث عرض البيانات
        """
        self.display_data()
        self.update_status("تم تحديث العرض")

    def on_item_double_click(self, event):
        """
        معالج النقر المزدوج
        """
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values and len(values) > 0:
                messagebox.showinfo("تفاصيل الصف", f"البيانات: {values[:3]}...")

    def update_status(self, message: str):
        """
        تحديث شريط الحالة
        """
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def run(self):
        """
        تشغيل التطبيق
        """
        self.root.mainloop()

def main():
    """
    تشغيل التطبيق المبسط
    """
    print("🚀 تشغيل برنامج البيانات المالية المبسط...")
    app = SimpleFinanceApp()
    app.run()

if __name__ == "__main__":
    main()
