# دليل البدء السريع - برنامج إدارة البيانات المالية للطلاب

## 🚀 التشغيل السريع

### الطريقة الأولى: استخدام ملف التشغيل
1. انقر نقراً مزدوجاً على `run_program.bat`
2. سيتم فحص المتطلبات وتشغيل البرنامج تلقائياً

### الطريقة الثانية: سطر الأوامر
```bash
python main.py
```

## 📋 خطوات الاستخدام السريع

### 1. تشغيل البرنامج لأول مرة
- ستظهر واجهة فارغة
- سيتم إنشاء قاعدة بيانات جديدة تلقائياً

### 2. اختبار البرنامج بالبيانات المثال
1. استخدم الملف `sample_students_data.xlsx` الموجود في المجلد
2. انقر على "استيراد ملف Excel"
3. اختر الملف `sample_students_data.xlsx`
4. ستظهر بيانات 15 طالب مثال

### 3. استكشاف الميزات
- **عرض البيانات**: ستظهر في الجدول الرئيسي
- **الإحصائيات**: انقر على زر "الإحصائيات"
- **التصدير**: انقر على "تصدير إلى Excel"
- **إنشاء قالب**: انقر على "إنشاء قالب"

## 📊 البيانات المثال المتضمنة

الملف `sample_students_data.xlsx` يحتوي على:
- 15 طالب من مراحل دراسية مختلفة
- بيانات أقساط وخصومات متنوعة
- مديونيات سابقة ومصروفات جديدة
- تواريخ وأرقام قيود للأقساط

## 🔧 استكشاف الأخطاء السريع

### المشكلة: "المكتبات مفقودة"
**الحل:**
```bash
pip install pandas openpyxl
```

### المشكلة: "خطأ في قراءة ملف Excel"
**الحل:**
- تأكد من أن الملف ليس مفتوحاً في Excel
- تحقق من صيغة الملف (.xlsx أو .xls)

### المشكلة: "لا يمكن حفظ الملف"
**الحل:**
- تأكد من أن ملف التصدير ليس مفتوحاً
- تحقق من صلاحيات الكتابة

## 📁 هيكل الملفات

```
move-finance/
├── main.py                     # الملف الرئيسي ⭐
├── run_program.bat             # ملف التشغيل السريع ⭐
├── sample_students_data.xlsx   # بيانات مثال للاختبار ⭐
├── school_finance.db           # قاعدة البيانات (تُنشأ تلقائياً)
├── gui_interface.py            # واجهة المستخدم
├── database_manager.py         # إدارة قاعدة البيانات
├── data_processor.py           # معالجة البيانات
├── excel_handler.py            # التعامل مع Excel
├── config.py                   # الإعدادات
├── requirements.txt            # المكتبات المطلوبة
├── README.md                   # دليل مفصل
└── requirements_documentation.md # توثيق المتطلبات
```

## 🎯 نصائح سريعة

### للمستخدمين الجدد:
1. ابدأ بتجربة البيانات المثال
2. اقرأ رسائل الحالة في أسفل البرنامج
3. استخدم زر "إنشاء قالب" لفهم تنسيق البيانات

### للاستخدام المتقدم:
1. راجع ملف `config.py` لتخصيص الإعدادات
2. استخدم `requirements_documentation.md` للتفاصيل التقنية
3. راجع `README.md` للدليل الشامل

## 📞 الحصول على المساعدة

1. **رسائل الخطأ**: اقرأ الرسالة بعناية - غالباً ما تحتوي على الحل
2. **شريط الحالة**: يعرض حالة العمليات الجارية
3. **الملفات المرجعية**: 
   - `README.md` للدليل الشامل
   - `requirements_documentation.md` للمتطلبات التقنية

## ✅ قائمة التحقق السريع

- [ ] Python مثبت (3.6+)
- [ ] المكتبات مثبتة (pandas, openpyxl)
- [ ] جميع الملفات موجودة في نفس المجلد
- [ ] تم اختبار البرنامج بالبيانات المثال
- [ ] تم فهم واجهة المستخدم الأساسية

---

**🎉 مبروك! أنت الآن جاهز لاستخدام برنامج إدارة البيانات المالية للطلاب**
