# -*- coding: utf-8 -*-
"""
إدارة قاعدة البيانات SQLite
"""

import sqlite3
import pandas as pd
from typing import List, Dict, Any, Optional
from config import DATABASE_NAME, DEFAULT_FEES, ACADEMIC_STAGES

class DatabaseManager:
    def __init__(self, db_name: str = DATABASE_NAME):
        """
        تهيئة مدير قاعدة البيانات
        """
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        """
        الحصول على اتصال بقاعدة البيانات
        """
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """
        إنشاء جداول قاعدة البيانات
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # إنشاء جدول بيانات الطلاب المالية
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS student_finance (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    total_count INTEGER DEFAULT 0,
                    serial_number TEXT,
                    file_opening_fee REAL DEFAULT 0,
                    file_opening_date TEXT,
                    transferred_file_opening REAL DEFAULT 0,
                    transferred_file_date TEXT,
                    batch_number TEXT,
                    academic_stage TEXT,
                    student_name TEXT NOT NULL,
                    previous_debt_2324 REAL DEFAULT 0,
                    old_year_fees_2324 REAL DEFAULT 0,
                    new_year_fees_2025 REAL DEFAULT 0,
                    installment_1 REAL DEFAULT 0,
                    installment_1_date TEXT,
                    installment_1_receipt TEXT,
                    installment_2 REAL DEFAULT 0,
                    installment_2_date TEXT,
                    installment_2_receipt TEXT,
                    installment_3 REAL DEFAULT 0,
                    installment_3_date TEXT,
                    installment_3_receipt TEXT,
                    installment_4 REAL DEFAULT 0,
                    installment_4_date TEXT,
                    installment_4_receipt TEXT,
                    installment_5 REAL DEFAULT 0,
                    installment_5_date TEXT,
                    installment_5_receipt TEXT,
                    installment_6 REAL DEFAULT 0,
                    installment_6_date TEXT,
                    installment_6_receipt TEXT,
                    installment_7 REAL DEFAULT 0,
                    installment_7_date TEXT,
                    installment_7_receipt TEXT,
                    installment_8 REAL DEFAULT 0,
                    installment_8_date TEXT,
                    installment_8_receipt TEXT,
                    installment_9 REAL DEFAULT 0,
                    installment_9_date TEXT,
                    installment_9_receipt TEXT,
                    total_paid REAL DEFAULT 0,
                    remaining_balance REAL DEFAULT 0,
                    sibling_discount REAL DEFAULT 0,
                    cash_discount REAL DEFAULT 0,
                    teacher_discount REAL DEFAULT 0,
                    transfer_discount REAL DEFAULT 0,
                    personal_discount REAL DEFAULT 0,
                    total_discounts REAL DEFAULT 0,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول هيكل الرسوم
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS fee_structure (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    academic_stage TEXT UNIQUE NOT NULL,
                    tuition_fees REAL NOT NULL,
                    registration_fee REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            
        # إدراج الرسوم الافتراضية إذا لم تكن موجودة
        self.init_default_fees()
    
    def init_default_fees(self):
        """
        إدراج الرسوم الافتراضية لكل مرحلة دراسية
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            for stage, fee in DEFAULT_FEES.items():
                cursor.execute('''
                    INSERT OR IGNORE INTO fee_structure (academic_stage, tuition_fees, registration_fee)
                    VALUES (?, ?, ?)
                ''', (stage, fee, 500))  # رسم فتح ملف افتراضي 500
            
            conn.commit()
    
    def insert_student_data(self, student_data: Dict[str, Any]) -> int:
        """
        إدراج بيانات طالب جديد
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            columns = ', '.join(student_data.keys())
            placeholders = ', '.join(['?' for _ in student_data])
            values = list(student_data.values())
            
            cursor.execute(f'''
                INSERT INTO student_finance ({columns})
                VALUES ({placeholders})
            ''', values)
            
            conn.commit()
            return cursor.lastrowid
    
    def update_student_data(self, student_id: int, student_data: Dict[str, Any]) -> bool:
        """
        تحديث بيانات طالب موجود
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                set_clause = ', '.join([f'{key} = ?' for key in student_data.keys()])
                values = list(student_data.values()) + [student_id]
                
                cursor.execute(f'''
                    UPDATE student_finance 
                    SET {set_clause}, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', values)
                
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في تحديث بيانات الطالب: {e}")
            return False
    
    def get_all_students(self) -> pd.DataFrame:
        """
        الحصول على جميع بيانات الطلاب
        """
        with self.get_connection() as conn:
            return pd.read_sql_query('SELECT * FROM student_finance ORDER BY student_name', conn)
    
    def get_student_by_id(self, student_id: int) -> Optional[Dict[str, Any]]:
        """
        الحصول على بيانات طالب بالمعرف
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM student_finance WHERE id = ?', (student_id,))
            row = cursor.fetchone()
            
            if row:
                columns = [description[0] for description in cursor.description]
                return dict(zip(columns, row))
            return None
    
    def delete_student(self, student_id: int) -> bool:
        """
        حذف بيانات طالب
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM student_finance WHERE id = ?', (student_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في حذف بيانات الطالب: {e}")
            return False
    
    def get_fee_structure(self) -> pd.DataFrame:
        """
        الحصول على هيكل الرسوم
        """
        with self.get_connection() as conn:
            return pd.read_sql_query('SELECT * FROM fee_structure ORDER BY academic_stage', conn)
    
    def update_fee_structure(self, stage: str, tuition_fees: float, registration_fee: float = 0) -> bool:
        """
        تحديث رسوم مرحلة دراسية
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO fee_structure (academic_stage, tuition_fees, registration_fee, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                ''', (stage, tuition_fees, registration_fee))
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في تحديث هيكل الرسوم: {e}")
            return False
    
    def clear_all_students(self) -> bool:
        """
        حذف جميع بيانات الطلاب
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM student_finance')
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في حذف جميع البيانات: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        الحصول على إحصائيات البيانات
        """
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # إجمالي عدد الطلاب
            cursor.execute('SELECT COUNT(*) FROM student_finance')
            total_students = cursor.fetchone()[0]
            
            # إجمالي المديونيات
            cursor.execute('SELECT SUM(remaining_balance) FROM student_finance')
            total_debt = cursor.fetchone()[0] or 0
            
            # إجمالي الأقساط المدفوعة
            cursor.execute('SELECT SUM(total_paid) FROM student_finance')
            total_paid = cursor.fetchone()[0] or 0
            
            # إحصائيات حسب المرحلة
            cursor.execute('''
                SELECT academic_stage, COUNT(*), SUM(remaining_balance), SUM(total_paid)
                FROM student_finance 
                GROUP BY academic_stage
            ''')
            stage_stats = cursor.fetchall()
            
            return {
                'total_students': total_students,
                'total_debt': total_debt,
                'total_paid': total_paid,
                'stage_statistics': stage_stats
            }
