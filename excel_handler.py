# -*- coding: utf-8 -*-
"""
التعامل مع ملفات Excel
"""

import pandas as pd
import os
from typing import Tuple, List, Optional
from tkinter import filedialog, messagebox
from config import STANDARD_COLUMNS

class ExcelHandler:
    def __init__(self):
        """
        تهيئة معالج ملفات Excel
        """
        self.supported_extensions = ['.xlsx', '.xls', '.csv']

    def import_excel_file(self, file_path: Optional[str] = None) -> Tuple[pd.DataFrame, List[str]]:
        """
        استيراد ملف Excel مع معالجة متقدمة للبيانات المالية
        """
        errors = []
        warnings = []

        try:
            # إذا لم يتم تحديد مسار الملف، اطلب من المستخدم اختياره
            if not file_path:
                file_path = filedialog.askopenfilename(
                    title="اختر ملف البيانات المالية القديمة",
                    filetypes=[
                        ("Excel files", "*.xlsx *.xls"),
                        ("CSV files", "*.csv"),
                        ("All files", "*.*")
                    ]
                )

            if not file_path:
                return pd.DataFrame(), ["لم يتم اختيار ملف"]

            # التحقق من وجود الملف
            if not os.path.exists(file_path):
                return pd.DataFrame(), [f"الملف غير موجود: {file_path}"]

            # التحقق من امتداد الملف
            file_extension = os.path.splitext(file_path)[1].lower()
            if file_extension not in self.supported_extensions:
                return pd.DataFrame(), [f"نوع الملف غير مدعوم: {file_extension}"]

            print(f"🔍 جاري قراءة الملف: {os.path.basename(file_path)}")

            # قراءة الملف مع معالجة متقدمة للبيانات المالية
            if file_extension == '.csv':
                df = pd.read_csv(
                    file_path,
                    encoding='utf-8-sig',
                    na_values=['', 'NULL', 'null', 'N/A', 'n/a', '#N/A', 'NaN'],
                    keep_default_na=True,
                    dtype=str  # قراءة كل شيء كنص أولاً
                )
            else:
                # استخدام openpyxl مع معالجة متقدمة
                df = pd.read_excel(
                    file_path,
                    engine='openpyxl',
                    na_values=['', 'NULL', 'null', 'N/A', 'n/a', '#N/A', 'NaN'],
                    keep_default_na=True,
                    dtype=str,  # قراءة كل شيء كنص أولاً
                    converters={}  # سنحدد المحولات لاحقاً
                )

            print(f"✅ تم قراءة {len(df)} صف و {len(df.columns)} عمود")

            # التحقق من وجود بيانات
            if df.empty:
                return pd.DataFrame(), ["الملف فارغ"]

            # التحقق من وجود أعمدة
            if len(df.columns) == 0:
                return pd.DataFrame(), ["الملف لا يحتوي على أعمدة"]

            # تنظيف أسماء الأعمدة
            df.columns = df.columns.astype(str).str.strip()

            # إزالة الصفوف الفارغة تماماً
            original_rows = len(df)
            df = df.dropna(how='all')

            if len(df) < original_rows:
                warnings.append(f"تم حذف {original_rows - len(df)} صف فارغ")

            if df.empty:
                return pd.DataFrame(), ["الملف لا يحتوي على بيانات صالحة بعد التنظيف"]

            # معالجة البيانات المالية بدقة
            df_processed = self._process_financial_data(df, warnings)

            # إضافة عمود ترقيم الصفوف
            df_processed.insert(0, 'رقم الصف', range(1, len(df_processed) + 1))

            # تقرير التحليل
            analysis_report = self._generate_import_analysis(df_processed, warnings)

            print("📊 تحليل البيانات المستوردة:")
            for line in analysis_report:
                print(f"   {line}")

            return df_processed, warnings

        except FileNotFoundError:
            return pd.DataFrame(), [f"الملف غير موجود: {file_path}"]
        except PermissionError:
            return pd.DataFrame(), [f"لا يمكن الوصول إلى الملف: {file_path}. تأكد من أن الملف غير مفتوح في برنامج آخر"]
        except pd.errors.EmptyDataError:
            return pd.DataFrame(), ["الملف فارغ أو تالف"]
        except Exception as e:
            return pd.DataFrame(), [f"خطأ في قراءة الملف: {str(e)}"]

    def _process_financial_data(self, df: pd.DataFrame, warnings: List[str]) -> pd.DataFrame:
        """
        معالجة البيانات المالية مع التعامل الصحيح مع القيم الفارغة والأصفار
        """
        df_processed = df.copy()

        # تحديد الأعمدة المالية (الأرقام)
        financial_columns = [
            'رسم فتح ملف', 'فتح ملف مرحل', 'مديونية سابقة 23/24',
            'مصروفات عام 2023/2024', 'مصروفات دراسية 2025',
            'القسط الأول', 'القسط الثاني', 'القسط الثالث', 'القسط الرابع',
            'القسط الخامس', 'القسط السادس', 'القسط السابع', 'القسط الثامن', 'القسط التاسع',
            'إجمالي الأقساط المدفوعة', 'المتبقي',
            'خصم إخوة', 'خصم كاش', 'خصم مدرسين', 'خصم تحويل', 'خصم شخصي', 'إجمالي الخصومات'
        ]

        # معالجة كل عمود مالي
        for col in df_processed.columns:
            if any(fin_col in col for fin_col in financial_columns):
                df_processed[col] = self._clean_financial_column(df_processed[col], col, warnings)

        # معالجة الأعمدة النصية
        text_columns = ['اسم الطالب', 'المرحلة', 'الدفعة', 'السيريال', 'ملاحظات']
        for col in df_processed.columns:
            if any(text_col in col for text_col in text_columns):
                df_processed[col] = self._clean_text_column(df_processed[col], col, warnings)

        # معالجة أعمدة التواريخ
        date_columns = ['تاريخ فتح الملف', 'تاريخ فتح الملف المرحل']
        for i in range(1, 10):
            date_columns.append(f'تاريخ القسط ال{"أول" if i == 1 else "ثاني" if i == 2 else "ثالث" if i == 3 else "رابع" if i == 4 else "خامس" if i == 5 else "سادس" if i == 6 else "سابع" if i == 7 else "ثامن" if i == 8 else "تاسع"}')

        for col in df_processed.columns:
            if any(date_col in col for date_col in date_columns):
                df_processed[col] = self._clean_date_column(df_processed[col], col, warnings)

        return df_processed

    def _clean_financial_column(self, series: pd.Series, col_name: str, warnings: List[str]) -> pd.Series:
        """
        تنظيف الأعمدة المالية مع التعامل الصحيح مع القيم الفارغة
        """
        cleaned = series.copy()

        # إحصائيات قبل التنظيف
        null_count = cleaned.isnull().sum()
        empty_count = (cleaned == '').sum()

        # تنظيف النصوص المالية
        cleaned = cleaned.astype(str)
        cleaned = cleaned.str.replace(',', '')  # إزالة الفواصل
        cleaned = cleaned.str.replace('جنيه', '')  # إزالة كلمة جنيه
        cleaned = cleaned.str.replace('ج.م', '')  # إزالة ج.م
        cleaned = cleaned.str.strip()  # إزالة المسافات

        # تحويل إلى أرقام مع معالجة الأخطاء
        def safe_convert_to_float(value):
            if pd.isna(value) or value == '' or value == 'nan' or value == 'None':
                return None  # الاحتفاظ بـ None بدلاً من 0
            try:
                return float(value)
            except (ValueError, TypeError):
                return None

        cleaned = cleaned.apply(safe_convert_to_float)

        # إحصائيات بعد التنظيف
        final_null_count = cleaned.isnull().sum()

        if final_null_count > 0:
            warnings.append(f"العمود '{col_name}': {final_null_count} قيمة فارغة (ستظهر كـ 'غير محدد')")

        return cleaned

    def _clean_text_column(self, series: pd.Series, col_name: str, warnings: List[str]) -> pd.Series:
        """
        تنظيف الأعمدة النصية
        """
        cleaned = series.copy()
        cleaned = cleaned.astype(str)
        cleaned = cleaned.str.strip()

        # استبدال القيم الفارغة
        cleaned = cleaned.replace(['nan', 'None', ''], None)

        null_count = cleaned.isnull().sum()
        if null_count > 0:
            warnings.append(f"العمود '{col_name}': {null_count} قيمة فارغة")

        return cleaned

    def _clean_date_column(self, series: pd.Series, col_name: str, warnings: List[str]) -> pd.Series:
        """
        تنظيف أعمدة التواريخ
        """
        cleaned = series.copy()

        # محاولة تحويل إلى تاريخ
        try:
            cleaned = pd.to_datetime(cleaned, errors='coerce')
        except:
            pass

        null_count = cleaned.isnull().sum()
        if null_count > 0:
            warnings.append(f"العمود '{col_name}': {null_count} تاريخ غير صحيح")

        return cleaned

    def _generate_import_analysis(self, df: pd.DataFrame, warnings: List[str]) -> List[str]:
        """
        إنشاء تقرير تحليل البيانات المستوردة
        """
        analysis = []
        analysis.append(f"إجمالي الصفوف: {len(df)}")
        analysis.append(f"إجمالي الأعمدة: {len(df.columns)}")

        # تحليل البيانات المالية
        financial_cols = [col for col in df.columns if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية'])]
        if financial_cols:
            analysis.append(f"الأعمدة المالية: {len(financial_cols)}")

            # حساب إجمالي القيم المالية
            total_values = 0
            null_values = 0
            for col in financial_cols:
                if col in df.columns:
                    col_data = pd.to_numeric(df[col], errors='coerce')
                    total_values += col_data.notna().sum()
                    null_values += col_data.isna().sum()

            analysis.append(f"القيم المالية الصحيحة: {total_values}")
            if null_values > 0:
                analysis.append(f"القيم المالية الفارغة: {null_values}")

        # تحليل أسماء الطلاب
        if 'اسم الطالب' in df.columns:
            student_names = df['اسم الطالب'].dropna()
            analysis.append(f"عدد الطلاب: {len(student_names)}")

            if len(student_names) != len(df):
                analysis.append(f"طلاب بدون أسماء: {len(df) - len(student_names)}")

        return analysis

    def export_to_excel(self, df: pd.DataFrame, file_path: Optional[str] = None) -> Tuple[bool, str]:
        """
        تصدير البيانات إلى ملف Excel
        """
        try:
            # إذا لم يتم تحديد مسار الملف، اطلب من المستخدم اختياره
            if not file_path:
                file_path = filedialog.asksaveasfilename(
                    title="حفظ ملف Excel",
                    defaultextension=".xlsx",
                    filetypes=[
                        ("Excel files", "*.xlsx"),
                        ("CSV files", "*.csv"),
                        ("All files", "*.*")
                    ]
                )

            if not file_path:
                return False, "لم يتم اختيار مسار للحفظ"

            # التحقق من وجود بيانات للتصدير
            if df.empty:
                return False, "لا توجد بيانات للتصدير"

            # تحديد نوع الملف
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension == '.csv':
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            else:
                # إنشاء ملف Excel مع تنسيق
                with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                    df.to_excel(writer, sheet_name='البيانات المالية', index=False)

                    # الحصول على ورقة العمل لتطبيق التنسيق
                    worksheet = writer.sheets['البيانات المالية']

                    # تطبيق تنسيق على رؤوس الأعمدة
                    from openpyxl.styles import Font, PatternFill, Alignment

                    header_font = Font(bold=True, color="FFFFFF")
                    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
                    header_alignment = Alignment(horizontal="center", vertical="center")

                    for col_num, column_title in enumerate(df.columns, 1):
                        cell = worksheet.cell(row=1, column=col_num)
                        cell.font = header_font
                        cell.fill = header_fill
                        cell.alignment = header_alignment

                    # ضبط عرض الأعمدة
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter

                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass

                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width

            return True, f"تم حفظ الملف بنجاح: {file_path}"

        except PermissionError:
            return False, f"لا يمكن الكتابة في الملف: {file_path}. تأكد من أن الملف غير مفتوح في برنامج آخر"
        except Exception as e:
            return False, f"خطأ في حفظ الملف: {str(e)}"

    def create_template_file(self, file_path: Optional[str] = None) -> Tuple[bool, str]:
        """
        إنشاء ملف قالب Excel بالأعمدة المطلوبة
        """
        try:
            # إذا لم يتم تحديد مسار الملف، اطلب من المستخدم اختياره
            if not file_path:
                file_path = filedialog.asksaveasfilename(
                    title="حفظ قالب Excel",
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
                )

            if not file_path:
                return False, "لم يتم اختيار مسار للحفظ"

            # إنشاء DataFrame فارغ بالأعمدة المطلوبة
            template_df = pd.DataFrame(columns=STANDARD_COLUMNS)

            # إضافة صف مثال
            example_row = {
                'العدد الإجمالي': 1,
                'السيريال': 'S001',
                'رسم فتح ملف': 500,
                'تاريخ فتح الملف': '2024-01-01',
                'فتح ملف مرحل': 0,
                'تاريخ فتح الملف المرحل': '',
                'الدفعة': 'دفعة 2024',
                'المرحلة': 'الابتدائية',
                'اسم الطالب': 'مثال على اسم الطالب',
                'مديونية سابقة 23/24': 1000,
                'مصروفات عام 2023/2024': 15000,
                'مصروفات دراسية 2025': 18000,
                'القسط الأول': 2000,
                'تاريخ القسط الأول': '2024-02-01',
                'رقم قيد القسط الأول': 'R001',
                'القسط الثاني': 2000,
                'تاريخ القسط الثاني': '2024-03-01',
                'رقم قيد القسط الثاني': 'R002',
                'القسط الثالث': 0,
                'تاريخ القسط الثالث': '',
                'رقم قيد القسط الثالث': '',
                'القسط الرابع': 0,
                'تاريخ القسط الرابع': '',
                'رقم قيد القسط الرابع': '',
                'القسط الخامس': 0,
                'تاريخ القسط الخامس': '',
                'رقم قيد القسط الخامس': '',
                'القسط السادس': 0,
                'تاريخ القسط السادس': '',
                'رقم قيد القسط السادس': '',
                'القسط السابع': 0,
                'تاريخ القسط السابع': '',
                'رقم قيد القسط السابع': '',
                'القسط الثامن': 0,
                'تاريخ القسط الثامن': '',
                'رقم قيد القسط الثامن': '',
                'القسط التاسع': 0,
                'تاريخ القسط التاسع': '',
                'رقم قيد القسط التاسع': '',
                'إجمالي الأقساط المدفوعة': 4000,
                'المتبقي': 15000,
                'خصم إخوة': 500,
                'خصم كاش': 0,
                'خصم مدرسين': 0,
                'خصم تحويل': 0,
                'خصم شخصي': 0,
                'إجمالي الخصومات': 500,
                'ملاحظات': 'مثال على الملاحظات'
            }

            template_df = pd.concat([template_df, pd.DataFrame([example_row])], ignore_index=True)

            # حفظ القالب
            success, message = self.export_to_excel(template_df, file_path)

            if success:
                return True, f"تم إنشاء قالب Excel بنجاح: {file_path}"
            else:
                return False, message

        except Exception as e:
            return False, f"خطأ في إنشاء القالب: {str(e)}"

    def validate_excel_structure(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        التحقق من هيكل ملف Excel
        """
        warnings = []

        # التحقق من وجود أعمدة أساسية
        essential_columns = ['اسم الطالب', 'المرحلة']
        missing_essential = []

        for col in essential_columns:
            found = False
            for df_col in df.columns:
                if col.lower() in df_col.lower():
                    found = True
                    break
            if not found:
                missing_essential.append(col)

        if missing_essential:
            warnings.append(f"الأعمدة الأساسية المفقودة: {', '.join(missing_essential)}")

        # التحقق من وجود بيانات في الأعمدة الأساسية
        for col in essential_columns:
            for df_col in df.columns:
                if col.lower() in df_col.lower():
                    if df[df_col].isna().all():
                        warnings.append(f"العمود '{df_col}' فارغ تماماً")
                    break

        # التحقق من تكرار أسماء الطلاب
        name_columns = [col for col in df.columns if 'اسم' in col.lower() or 'name' in col.lower()]
        if name_columns:
            name_col = name_columns[0]
            duplicates = df[df.duplicated(subset=[name_col], keep=False)]
            if not duplicates.empty:
                warnings.append(f"يوجد {len(duplicates)} طالب بأسماء مكررة")

        return len(warnings) == 0, warnings
