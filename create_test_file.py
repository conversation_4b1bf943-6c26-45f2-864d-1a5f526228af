#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف اختبار بسيط للبيانات المالية
"""

import pandas as pd

def main():
    """
    إنشاء ملف اختبار بسيط
    """
    print("📝 إنشاء ملف اختبار للبيانات المالية...")
    
    # بيانات اختبار بسيطة مع خلايا فارغة حقيقية
    data = {
        'اسم الطالب': [
            'أحمد محمد علي',
            'فاطمة حسن محمود',
            'محمد عبدالله أحمد',
            'نور الهدى محمد',
            'يوسف إبراهيم حسن'
        ],
        'المرحلة': [
            'الابتدائية',
            'الإعدادية',
            'الثانوية',
            'الابتدائية',
            'الإعدادية'
        ],
        'مديونية سابقة 23/24': [
            1000,
            None,  # خلية فارغة
            2500,
            0,
            1500
        ],
        'مصروفات عام 2023/2024': [
            15000,
            18000,
            None,  # خلية فارغة
            15000,
            18000
        ],
        'مصروفات دراسية 2025': [
            18000,
            20000,
            25000,
            None,  # خلية فارغة
            20000
        ],
        'القسط الأول': [
            2000,
            3000,
            2500,
            None,  # خلية فارغة
            3000
        ],
        'القسط الثاني': [
            None,  # خلية فارغة
            3000,
            2500,
            2000,
            0
        ],
        'القسط الثالث': [
            0,
            None,  # خلية فارغة
            0,
            0,
            3000
        ],
        'خصم إخوة': [
            500,
            0,
            None,  # خلية فارغة
            500,
            1000
        ],
        'خصم كاش': [
            0,
            None,  # خلية فارغة
            200,
            0,
            0
        ],
        'ملاحظات': [
            'طالب متفوق',
            None,  # خلية فارغة
            'يحتاج متابعة',
            'منتظم في الدفع',
            None  # خلية فارغة
        ]
    }
    
    try:
        # إنشاء DataFrame
        df = pd.DataFrame(data)
        
        # حفظ الملف
        filename = 'test_financial_data.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')
        
        print(f"✅ تم إنشاء ملف الاختبار: {filename}")
        print(f"📊 البيانات: {len(df)} صف و {len(df.columns)} عمود")
        
        # إحصائيات الخلايا الفارغة
        empty_cells = df.isnull().sum().sum()
        total_cells = len(df) * len(df.columns)
        
        print(f"\n📈 إحصائيات:")
        print(f"   📊 إجمالي الخلايا: {total_cells}")
        print(f"   🔴 خلايا فارغة: {empty_cells}")
        print(f"   ✅ خلايا مملوءة: {total_cells - empty_cells}")
        
        # عرض عينة من البيانات
        print(f"\n📋 عينة من البيانات:")
        print(df.head(3).to_string())
        
        print(f"\n🎯 الملف جاهز للاختبار!")
        print(f"💡 يمكنك الآن فتح البرنامج واستيراد الملف: {filename}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")

if __name__ == "__main__":
    main()
