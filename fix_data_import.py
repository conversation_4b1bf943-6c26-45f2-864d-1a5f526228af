#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from database_manager import DatabaseManager
from excel_handler import ExcelHandler
from data_processor import DataProcessor
import pandas as pd

def fix_data_import():
    print("🗑️ مسح قاعدة البيانات...")
    db = DatabaseManager()
    db.clear_all_students()
    
    print("📁 قراءة ملف Excel...")
    handler = ExcelHandler()
    df, warnings = handler.import_excel_file('sample_students_data.xlsx')
    print(f"تم قراءة {len(df)} صف من Excel")
    
    # طباعة عينة من البيانات المقروءة
    print("عينة من البيانات المقروءة:")
    if not df.empty:
        print(df.columns.tolist()[:5])
        print(df.head(2))
    else:
        print("ملف Excel فارغ!")
        return
    
    print("🔄 معالجة البيانات...")
    processor = DataProcessor(db)
    processed_df, errors = processor.process_excel_data(df)
    print(f"تم معالجة {len(processed_df)} صف")
    
    if errors:
        print("أخطاء في المعالجة:")
        for error in errors[:5]:  # أول 5 أخطاء فقط
            print(f"  - {error}")
    
    print("💾 حفظ في قاعدة البيانات...")
    records = processor.prepare_for_database(processed_df)
    print(f"تم تحضير {len(records)} سجل للحفظ")
    
    # حفظ كل سجل
    success_count = 0
    for i, record in enumerate(records):
        try:
            db.insert_student_data(record)
            success_count += 1
            if i < 3:  # طباعة أول 3 سجلات للتحقق
                student_name = record.get("student_name", "لا يوجد اسم")
                print(f"سجل {i+1}: {student_name}")
        except Exception as e:
            print(f"خطأ في حفظ السجل {i+1}: {e}")
            if i < 5:  # طباعة أول 5 أخطاء فقط
                print(f"  البيانات: {record}")
            break
    
    print(f"✅ تم حفظ {success_count} سجل بنجاح")
    
    # التحقق من البيانات المحفوظة
    print("🔍 التحقق من البيانات المحفوظة...")
    saved_data = db.get_all_students()
    print(f"عدد السجلات في قاعدة البيانات: {len(saved_data)}")
    
    if not saved_data.empty:
        print("عينة من البيانات المحفوظة:")
        print(saved_data[['student_name', 'stage', 'first_installment']].head(3))
    else:
        print("قاعدة البيانات فارغة!")

if __name__ == "__main__":
    fix_data_import()
