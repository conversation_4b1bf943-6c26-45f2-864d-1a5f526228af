#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل النظام المالي الكامل مع جميع الميزات
"""

import os
import sys

def main():
    """
    تشغيل النظام المالي الكامل
    """
    print("🎯 نظام إدارة البيانات المالية الكامل")
    print("=" * 60)
    
    print("📋 الملفات المتوفرة:")
    
    # فحص الملفات المتوفرة
    files_status = {
        'قالب_البيانات_المالية_المثالي.xlsx': os.path.exists('قالب_البيانات_المالية_المثالي.xlsx'),
        'قالب_فارغ_للملء_اليدوي.xlsx': os.path.exists('قالب_فارغ_للملء_اليدوي.xlsx'),
        'تعليمات_استخدام_القوالب.txt': os.path.exists('تعليمات_استخدام_القوالب.txt'),
        'simple_finance_app.py': os.path.exists('simple_finance_app.py')
    }
    
    for file, exists in files_status.items():
        status = "✅" if exists else "❌"
        print(f"   {status} {file}")
    
    print("\n🎯 الخطوات المطلوبة:")
    print("1️⃣  إنشاء القوالب (إذا لم تكن موجودة)")
    print("2️⃣  ملء القالب ببياناتك")
    print("3️⃣  تشغيل البرنامج")
    print("4️⃣  استيراد البيانات")
    print("5️⃣  نقل للسنة الجديدة")
    
    print("\n🤔 ماذا تريد أن تفعل؟")
    print("1️⃣  إنشاء القوالب")
    print("2️⃣  تشغيل البرنامج المالي")
    print("3️⃣  عرض التعليمات")
    print("4️⃣  تشغيل النظام الكامل")
    
    try:
        choice = input("\n👆 اختر رقم (1-4): ").strip()
        
        if choice == "1":
            print("🎨 إنشاء القوالب...")
            os.system("python create_perfect_template.py")
            
        elif choice == "2":
            print("🚀 تشغيل البرنامج المالي...")
            os.system("python simple_finance_app.py")
            
        elif choice == "3":
            print("📋 عرض التعليمات...")
            if os.path.exists('تعليمات_استخدام_القوالب.txt'):
                with open('تعليمات_استخدام_القوالب.txt', 'r', encoding='utf-8') as f:
                    print(f.read())
            else:
                print("❌ ملف التعليمات غير موجود. قم بإنشاء القوالب أولاً.")
                
        elif choice == "4":
            print("🎯 تشغيل النظام الكامل...")
            
            # إنشاء القوالب إذا لم تكن موجودة
            if not files_status['قالب_البيانات_المالية_المثالي.xlsx']:
                print("📝 إنشاء القوالب...")
                os.system("python create_perfect_template.py")
            
            print("\n✅ القوالب جاهزة!")
            print("💡 الآن:")
            print("   1. افتح ملف: قالب_فارغ_للملء_اليدوي.xlsx")
            print("   2. املأه ببياناتك")
            print("   3. احفظ الملف")
            print("   4. ارجع هنا واضغط Enter لتشغيل البرنامج")
            
            input("\n⏳ اضغط Enter بعد ملء القالب...")
            
            print("🚀 تشغيل البرنامج...")
            os.system("python simple_finance_app.py")
            
        else:
            print("❌ اختيار غير صحيح")
            
    except KeyboardInterrupt:
        print("\n👋 تم إلغاء التشغيل")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")

if __name__ == "__main__":
    main()
