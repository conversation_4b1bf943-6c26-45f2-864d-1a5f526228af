#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وتشخيص مشاكل الاستيراد
"""

import pandas as pd
import os
from excel_handler import ExcelHandler
from data_processor import DataProcessor
from database_manager import DatabaseManager

def test_excel_import():
    """
    اختبار استيراد ملف Excel خطوة بخطوة
    """
    print("=" * 60)
    print("🔍 تشخيص مشاكل استيراد البيانات المالية")
    print("=" * 60)
    
    # تهيئة المكونات
    excel_handler = ExcelHandler()
    db_manager = DatabaseManager()
    data_processor = DataProcessor(db_manager)
    
    # البحث عن ملفات Excel في المجلد
    excel_files = []
    for file in os.listdir('.'):
        if file.endswith(('.xlsx', '.xls')):
            excel_files.append(file)
    
    print(f"📁 ملفات Excel الموجودة: {excel_files}")
    
    if not excel_files:
        print("❌ لا توجد ملفات Excel في المجلد")
        return
    
    # اختبار أول ملف موجود
    test_file = excel_files[0]
    print(f"\n🧪 اختبار الملف: {test_file}")
    
    try:
        # الخطوة 1: قراءة الملف الخام
        print("\n📖 الخطوة 1: قراءة الملف الخام...")
        df_raw = pd.read_excel(test_file, engine='openpyxl')
        print(f"✅ تم قراءة {len(df_raw)} صف و {len(df_raw.columns)} عمود")
        print(f"📋 أسماء الأعمدة: {list(df_raw.columns)}")
        
        # عرض عينة من البيانات
        print(f"\n📊 عينة من البيانات (أول 3 صفوف):")
        print(df_raw.head(3).to_string())
        
        # الخطوة 2: استخدام معالج Excel المحسن
        print(f"\n🔧 الخطوة 2: استخدام معالج Excel المحسن...")
        df_processed, warnings = excel_handler.import_excel_file(test_file)
        
        if warnings:
            print(f"⚠️ تحذيرات: {len(warnings)}")
            for warning in warnings[:5]:
                print(f"   - {warning}")
        
        print(f"✅ المعالج المحسن: {len(df_processed)} صف و {len(df_processed.columns)} عمود")
        
        # الخطوة 3: تخطيط الأعمدة
        print(f"\n🗺️ الخطوة 3: تخطيط الأعمدة...")
        mapped_df = data_processor.map_columns(df_processed)
        print(f"✅ تم تخطيط {len(mapped_df.columns)} عمود")
        
        # الخطوة 4: تنظيف البيانات
        print(f"\n🧹 الخطوة 4: تنظيف البيانات...")
        cleaned_df = data_processor.clean_data(mapped_df)
        print(f"✅ تم تنظيف البيانات")
        
        # الخطوة 5: حساب البيانات المالية
        print(f"\n💰 الخطوة 5: حساب البيانات المالية...")
        calculated_df = data_processor.calculate_financial_data(cleaned_df)
        print(f"✅ تم حساب البيانات المالية")
        
        # عرض النتيجة النهائية
        print(f"\n📈 النتيجة النهائية:")
        print(f"   📊 الصفوف: {len(calculated_df)}")
        print(f"   📋 الأعمدة: {len(calculated_df.columns)}")
        
        # فحص البيانات المالية
        financial_cols = ['اسم الطالب', 'المرحلة', 'مديونية سابقة 23/24', 'القسط الأول', 'القسط الثاني']
        available_cols = [col for col in financial_cols if col in calculated_df.columns]
        
        if available_cols:
            print(f"\n💰 عينة من البيانات المالية:")
            sample_data = calculated_df[available_cols].head(3)
            print(sample_data.to_string())
            
            # فحص القيم الفارغة
            print(f"\n🔍 فحص القيم الفارغة:")
            for col in available_cols:
                null_count = calculated_df[col].isnull().sum()
                zero_count = (calculated_df[col] == 0).sum() if col != 'اسم الطالب' and col != 'المرحلة' else 0
                print(f"   {col}: {null_count} فارغة، {zero_count} أصفار")
        
        # الخطوة 6: تحضير لقاعدة البيانات
        print(f"\n💾 الخطوة 6: تحضير لقاعدة البيانات...")
        records = data_processor.prepare_for_database(calculated_df)
        print(f"✅ تم تحضير {len(records)} سجل")
        
        if records:
            print(f"\n📝 عينة من السجل الأول:")
            first_record = records[0]
            for key, value in list(first_record.items())[:10]:
                print(f"   {key}: {value}")
        
        print(f"\n🎉 تم الاختبار بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()

def test_column_mapping():
    """
    اختبار تخطيط الأعمدة بشكل منفصل
    """
    print("\n" + "=" * 60)
    print("🗺️ اختبار تخطيط الأعمدة")
    print("=" * 60)
    
    # إنشاء بيانات تجريبية
    test_data = {
        'اسم الطالب': ['أحمد محمد', 'فاطمة علي'],
        'المرحلة': ['الابتدائية', 'الثانوية'],
        'مديونية سابقة': [1000, 2000],
        'القسط الأول': [500, 1000],
        'خصم إخوة': [100, 0]
    }
    
    df_test = pd.DataFrame(test_data)
    print(f"📊 بيانات تجريبية: {len(df_test)} صف")
    print(f"📋 الأعمدة: {list(df_test.columns)}")
    
    # اختبار التخطيط
    db_manager = DatabaseManager()
    data_processor = DataProcessor(db_manager)
    
    mapped_df = data_processor.map_columns(df_test)
    print(f"✅ تم تخطيط {len(mapped_df.columns)} عمود")
    
    # عرض النتيجة
    print(f"\n📈 النتيجة:")
    print(mapped_df.head().to_string())

if __name__ == "__main__":
    test_excel_import()
    test_column_mapping()
