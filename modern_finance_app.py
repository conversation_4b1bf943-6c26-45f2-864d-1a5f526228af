#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مالي حديث باستخدام PyQt5 (البديل الأفضل لـ jQuery Python)
"""

try:
    from PyQt5.QtWidgets import *
    from PyQt5.QtCore import *
    from PyQt5.QtGui import *
    import sys
    import pandas as pd
    import os
    from typing import Optional
    
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    print("❌ PyQt5 غير متوفر. يمكن تثبيته بالأمر: pip install PyQt5")

class ModernFinanceApp(QMainWindow):
    """
    تطبيق مالي حديث وجميل باستخدام PyQt5
    """
    
    def __init__(self):
        super().__init__()
        self.current_data = pd.DataFrame()
        self.init_ui()
    
    def init_ui(self):
        """
        تهيئة واجهة المستخدم الحديثة
        """
        # إعداد النافذة الرئيسية
        self.setWindowTitle("💰 برنامج البيانات المالية الحديث - PyQt5")
        self.setWindowIcon(QIcon())
        
        # تحديد حجم النافذة (90% من الشاشة)
        screen = QApplication.desktop().screenGeometry()
        width = int(screen.width() * 0.9)
        height = int(screen.height() * 0.9)
        self.resize(width, height)
        
        # توسيط النافذة
        self.move((screen.width() - width) // 2, (screen.height() - height) // 2)
        
        # إعداد الأنماط الحديثة
        self.setup_modern_styles()
        
        # إنشاء الواجهة
        self.create_ui()
        
        # شريط الحالة
        self.statusBar().showMessage("جاهز للاستخدام")
    
    def setup_modern_styles(self):
        """
        إعداد الأنماط الحديثة والجميلة
        """
        style = """
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QToolBar {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #4a90e2, stop: 1 #357abd);
            border: none;
            spacing: 10px;
            padding: 10px;
        }
        
        QToolBar QToolButton {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            padding: 8px 16px;
            margin: 2px;
        }
        
        QToolBar QToolButton:hover {
            background: rgba(255, 255, 255, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }
        
        QToolBar QToolButton:pressed {
            background: rgba(255, 255, 255, 0.1);
        }
        
        QTableWidget {
            background-color: white;
            alternate-background-color: #f8f9fa;
            selection-background-color: #4a90e2;
            gridline-color: #e0e0e0;
            font-size: 11px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        QTableWidget::item:selected {
            background-color: #4a90e2;
            color: white;
        }
        
        QHeaderView::section {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #4a90e2, stop: 1 #357abd);
            color: white;
            font-weight: bold;
            font-size: 12px;
            padding: 10px;
            border: none;
            border-right: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        QHeaderView::section:hover {
            background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                      stop: 0 #5ba0f2, stop: 1 #4080cd);
        }
        
        QStatusBar {
            background: #2c3e50;
            color: white;
            font-size: 11px;
            padding: 5px;
        }
        
        QMessageBox {
            background-color: white;
            font-size: 11px;
        }
        
        QFileDialog {
            background-color: white;
        }
        """
        
        self.setStyleSheet(style)
    
    def create_ui(self):
        """
        إنشاء واجهة المستخدم
        """
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # شريط الأدوات
        self.create_toolbar()
        
        # معلومات الملف
        self.file_info_label = QLabel("لم يتم تحميل ملف")
        self.file_info_label.setStyleSheet("""
            QLabel {
                background: #e8f4fd;
                border: 1px solid #4a90e2;
                border-radius: 6px;
                padding: 8px;
                font-style: italic;
                color: #2c3e50;
            }
        """)
        layout.addWidget(self.file_info_label)
        
        # جدول البيانات
        self.create_table(layout)
        
        # معلومات إضافية
        info_layout = QHBoxLayout()
        
        self.count_label = QLabel("عدد الصفوف: 0")
        self.count_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        info_layout.addWidget(self.count_label)
        
        info_layout.addStretch()
        
        self.stats_label = QLabel("إحصائيات: غير متوفرة")
        self.stats_label.setStyleSheet("color: #7f8c8d;")
        info_layout.addWidget(self.stats_label)
        
        layout.addLayout(info_layout)
    
    def create_toolbar(self):
        """
        إنشاء شريط الأدوات الحديث
        """
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setMovable(False)
        
        # أزرار الأدوات
        import_action = QAction("📁 استيراد Excel", self)
        import_action.triggered.connect(self.import_excel)
        toolbar.addAction(import_action)
        
        export_action = QAction("💾 تصدير Excel", self)
        export_action.triggered.connect(self.export_excel)
        toolbar.addAction(export_action)
        
        template_action = QAction("📋 إنشاء قالب", self)
        template_action.triggered.connect(self.create_template)
        toolbar.addAction(template_action)
        
        toolbar.addSeparator()
        
        refresh_action = QAction("🔄 تحديث", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
        
        clear_action = QAction("🗑️ مسح", self)
        clear_action.triggered.connect(self.clear_data)
        toolbar.addAction(clear_action)
    
    def create_table(self, layout):
        """
        إنشاء جدول البيانات الحديث
        """
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.table.setSortingEnabled(True)
        self.table.verticalHeader().setVisible(False)
        
        # ربط الأحداث
        self.table.itemDoubleClicked.connect(self.on_item_double_click)
        
        layout.addWidget(self.table)
    
    def import_excel(self):
        """
        استيراد ملف Excel مع معالجة محسنة
        """
        try:
            self.statusBar().showMessage("جاري اختيار الملف...")
            
            # اختيار الملف
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف البيانات المالية",
                "",
                "Excel files (*.xlsx *.xls);;CSV files (*.csv);;All files (*.*)"
            )
            
            if not file_path:
                self.statusBar().showMessage("لم يتم اختيار ملف")
                return
            
            self.statusBar().showMessage("جاري قراءة الملف...")
            
            # قراءة الملف
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8-sig')
            else:
                df = pd.read_excel(file_path, engine='openpyxl')
            
            if df.empty:
                QMessageBox.warning(self, "تحذير", "الملف فارغ")
                self.statusBar().showMessage("الملف فارغ")
                return
            
            # معالجة البيانات - حل مشكلة الأصفار
            self.statusBar().showMessage("جاري معالجة البيانات...")
            
            # استبدال القيم الفارغة بـ "غير محدد"
            for col in df.columns:
                if df[col].dtype == 'object':  # أعمدة نصية
                    df[col] = df[col].fillna('غير محدد')
                else:  # أعمدة رقمية
                    df[col] = df[col].fillna('غير محدد')
            
            # حفظ البيانات
            self.current_data = df
            
            # عرض البيانات
            self.display_data()
            
            # تحديث معلومات الملف
            file_name = os.path.basename(file_path)
            self.file_info_label.setText(f"📁 الملف المحمل: {file_name} | 📊 الصفوف: {len(df)} | 📋 الأعمدة: {len(df.columns)}")
            
            # رسالة النجاح
            QMessageBox.information(
                self, 
                "نجح الاستيراد",
                f"تم استيراد البيانات بنجاح!\n\n"
                f"📊 عدد الصفوف: {len(df)}\n"
                f"📋 عدد الأعمدة: {len(df.columns)}\n"
                f"📁 الملف: {file_name}"
            )
            
            self.statusBar().showMessage(f"تم استيراد {len(df)} صف بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في استيراد الملف:\n{str(e)}")
            self.statusBar().showMessage("خطأ في الاستيراد")
    
    def display_data(self):
        """
        عرض البيانات في الجدول الحديث
        """
        if self.current_data.empty:
            return
        
        # تحديد أبعاد الجدول
        rows, cols = self.current_data.shape
        self.table.setRowCount(rows)
        self.table.setColumnCount(cols)
        
        # تعيين رؤوس الأعمدة
        self.table.setHorizontalHeaderLabels(list(self.current_data.columns))
        
        # ملء البيانات
        for i in range(rows):
            for j in range(cols):
                value = self.current_data.iloc[i, j]
                if pd.isna(value):
                    item_text = 'غير محدد'
                else:
                    item_text = str(value)
                
                item = QTableWidgetItem(item_text)
                
                # تلوين الخلايا حسب النوع
                if item_text == 'غير محدد':
                    item.setBackground(QColor('#fff3cd'))  # أصفر فاتح
                elif isinstance(value, (int, float)) and value > 0:
                    item.setBackground(QColor('#d4edda'))  # أخضر فاتح
                
                self.table.setItem(i, j, item)
        
        # تعديل عرض الأعمدة
        self.table.resizeColumnsToContents()
        
        # تحديث الإحصائيات
        self.update_stats()
    
    def update_stats(self):
        """
        تحديث الإحصائيات
        """
        if self.current_data.empty:
            self.count_label.setText("عدد الصفوف: 0")
            self.stats_label.setText("إحصائيات: غير متوفرة")
            return
        
        rows = len(self.current_data)
        cols = len(self.current_data.columns)
        
        # حساب القيم الفارغة
        empty_cells = self.current_data.isnull().sum().sum()
        total_cells = rows * cols
        
        self.count_label.setText(f"عدد الصفوف: {rows}")
        self.stats_label.setText(f"إحصائيات: {cols} عمود | {empty_cells}/{total_cells} خلية فارغة")
    
    def export_excel(self):
        """
        تصدير البيانات إلى Excel
        """
        try:
            if self.current_data.empty:
                QMessageBox.warning(self, "تحذير", "لا توجد بيانات للتصدير")
                return
            
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف Excel",
                "",
                "Excel files (*.xlsx);;All files (*.*)"
            )
            
            if not file_path:
                return
            
            self.statusBar().showMessage("جاري تصدير البيانات...")
            
            # حفظ الملف
            self.current_data.to_excel(file_path, index=False, engine='openpyxl')
            
            QMessageBox.information(self, "نجح التصدير", f"تم حفظ الملف بنجاح:\n{file_path}")
            self.statusBar().showMessage("تم التصدير بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في تصدير الملف:\n{str(e)}")
            self.statusBar().showMessage("خطأ في التصدير")
    
    def create_template(self):
        """
        إنشاء قالب Excel
        """
        try:
            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ قالب Excel",
                "قالب_البيانات_المالية.xlsx",
                "Excel files (*.xlsx);;All files (*.*)"
            )
            
            if not file_path:
                return
            
            # إنشاء قالب
            template_columns = [
                'اسم الطالب', 'المرحلة', 'مديونية سابقة 23/24', 
                'مصروفات عام 2023/2024', 'مصروفات دراسية 2025',
                'القسط الأول', 'القسط الثاني', 'القسط الثالث',
                'خصم إخوة', 'خصم كاش', 'ملاحظات'
            ]
            
            template_df = pd.DataFrame(columns=template_columns)
            
            # إضافة صف مثال
            example_row = {
                'اسم الطالب': 'مثال على اسم الطالب',
                'المرحلة': 'الابتدائية',
                'مديونية سابقة 23/24': 1000,
                'مصروفات عام 2023/2024': 15000,
                'مصروفات دراسية 2025': 18000,
                'القسط الأول': 2000,
                'القسط الثاني': 2000,
                'القسط الثالث': 0,
                'خصم إخوة': 500,
                'خصم كاش': 0,
                'ملاحظات': 'مثال على الملاحظات'
            }
            
            template_df = pd.concat([template_df, pd.DataFrame([example_row])], ignore_index=True)
            
            # حفظ القالب
            template_df.to_excel(file_path, index=False, engine='openpyxl')
            
            QMessageBox.information(self, "نجح إنشاء القالب", f"تم إنشاء القالب بنجاح:\n{file_path}")
            self.statusBar().showMessage("تم إنشاء القالب بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء القالب:\n{str(e)}")
            self.statusBar().showMessage("خطأ في إنشاء القالب")
    
    def refresh_data(self):
        """
        تحديث عرض البيانات
        """
        self.display_data()
        self.statusBar().showMessage("تم تحديث العرض")
    
    def clear_data(self):
        """
        مسح البيانات
        """
        reply = QMessageBox.question(
            self, 
            "تأكيد المسح", 
            "هل أنت متأكد من مسح جميع البيانات؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.current_data = pd.DataFrame()
            self.table.clear()
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            self.file_info_label.setText("لم يتم تحميل ملف")
            self.update_stats()
            self.statusBar().showMessage("تم مسح البيانات")
    
    def on_item_double_click(self, item):
        """
        معالج النقر المزدوج
        """
        row = item.row()
        col = item.column()
        value = item.text()
        
        QMessageBox.information(
            self, 
            "تفاصيل الخلية",
            f"الصف: {row + 1}\n"
            f"العمود: {self.table.horizontalHeaderItem(col).text()}\n"
            f"القيمة: {value}"
        )

def run_modern_app():
    """
    تشغيل التطبيق الحديث
    """
    if not PYQT_AVAILABLE:
        print("❌ PyQt5 غير متوفر")
        print("💡 يمكن تثبيته بالأمر: pip install PyQt5")
        print("🔄 سيتم تشغيل النسخة المبسطة بدلاً من ذلك...")
        
        # تشغيل النسخة المبسطة كبديل
        from simple_finance_app import SimpleFinanceApp
        app = SimpleFinanceApp()
        app.run()
        return
    
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # نمط حديث
    
    # تطبيق نمط داكن اختياري
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.WindowText, QColor(255, 255, 255))
    # app.setPalette(palette)  # إلغاء التعليق للنمط الداكن
    
    window = ModernFinanceApp()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    run_modern_app()
