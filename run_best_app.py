#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل أفضل واجهة متاحة للبرنامج المالي
"""

import sys
import os

def check_requirements():
    """
    فحص المتطلبات والمكتبات المتاحة
    """
    print("🔍 فحص المتطلبات والمكتبات المتاحة...")
    
    requirements = {
        'pandas': False,
        'openpyxl': False,
        'tkinter': False,
        'PyQt5': False
    }
    
    # فحص pandas
    try:
        import pandas
        requirements['pandas'] = True
        print("✅ pandas متوفر")
    except ImportError:
        print("❌ pandas غير متوفر")
    
    # فحص openpyxl
    try:
        import openpyxl
        requirements['openpyxl'] = True
        print("✅ openpyxl متوفر")
    except ImportError:
        print("❌ openpyxl غير متوفر")
    
    # فحص tkinter
    try:
        import tkinter
        requirements['tkinter'] = True
        print("✅ tkinter متوفر")
    except ImportError:
        print("❌ tkinter غير متوفر")
    
    # فحص PyQt5
    try:
        from PyQt5.QtWidgets import QApplication
        requirements['PyQt5'] = True
        print("✅ PyQt5 متوفر")
    except ImportError:
        print("❌ PyQt5 غير متوفر")
    
    return requirements

def install_missing_packages():
    """
    تثبيت الحزم المفقودة
    """
    print("\n📦 تثبيت الحزم المفقودة...")
    
    required_packages = ['pandas', 'openpyxl']
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} متوفر")
        except ImportError:
            print(f"📥 تثبيت {package}...")
            os.system(f"pip install {package}")

def run_best_available_app():
    """
    تشغيل أفضل واجهة متاحة
    """
    print("=" * 60)
    print("🚀 برنامج إدارة البيانات المالية")
    print("=" * 60)
    
    # فحص المتطلبات
    requirements = check_requirements()
    
    # تثبيت الحزم الأساسية إذا لزم الأمر
    if not requirements['pandas'] or not requirements['openpyxl']:
        install_missing_packages()
    
    print("\n🎯 اختيار أفضل واجهة متاحة...")
    
    # الخيار الأول: PyQt5 (الأفضل والأحدث)
    if requirements['PyQt5'] and requirements['pandas'] and requirements['openpyxl']:
        print("🌟 تشغيل الواجهة الحديثة (PyQt5) - الأفضل!")
        print("✨ مميزات: واجهة حديثة، ألوان جميلة، أداء عالي")
        try:
            from modern_finance_app import run_modern_app
            run_modern_app()
            return
        except Exception as e:
            print(f"❌ خطأ في تشغيل الواجهة الحديثة: {e}")
    
    # الخيار الثاني: النسخة المبسطة (tkinter)
    if requirements['tkinter'] and requirements['pandas'] and requirements['openpyxl']:
        print("🔧 تشغيل الواجهة المبسطة (tkinter) - موثوقة!")
        print("✨ مميزات: بسيطة، سريعة، تعمل على جميع الأنظمة")
        try:
            from simple_finance_app import SimpleFinanceApp
            app = SimpleFinanceApp()
            app.run()
            return
        except Exception as e:
            print(f"❌ خطأ في تشغيل الواجهة المبسطة: {e}")
    
    # الخيار الثالث: الواجهة المحسنة الأصلية
    if requirements['tkinter'] and requirements['pandas']:
        print("⚡ تشغيل الواجهة المحسنة الأصلية")
        print("✨ مميزات: فلاتر متقدمة، شريط جانبي، إحصائيات")
        try:
            from enhanced_gui import run_enhanced_gui
            run_enhanced_gui()
            return
        except Exception as e:
            print(f"❌ خطأ في تشغيل الواجهة المحسنة: {e}")
    
    # الخيار الأخير: الواجهة العادية
    if requirements['tkinter']:
        print("📋 تشغيل الواجهة العادية")
        print("✨ مميزات: أساسية، مستقرة")
        try:
            from gui_interface import FinanceGUI
            app = FinanceGUI()
            app.run()
            return
        except Exception as e:
            print(f"❌ خطأ في تشغيل الواجهة العادية: {e}")
    
    # إذا فشل كل شيء
    print("❌ لا يمكن تشغيل أي واجهة!")
    print("💡 تأكد من تثبيت المتطلبات:")
    print("   pip install pandas openpyxl")
    print("   pip install PyQt5  # للواجهة الحديثة (اختياري)")

def show_app_comparison():
    """
    عرض مقارنة بين الواجهات المختلفة
    """
    print("\n" + "=" * 60)
    print("📊 مقارنة الواجهات المتاحة")
    print("=" * 60)
    
    comparison = """
🌟 الواجهة الحديثة (PyQt5) - الأفضل:
   ✅ تصميم حديث وجميل
   ✅ ألوان متدرجة وأنيقة
   ✅ أداء عالي وسرعة
   ✅ فرز تفاعلي للأعمدة
   ✅ تلوين ذكي للبيانات
   ❌ تحتاج تثبيت PyQt5

🔧 الواجهة المبسطة (tkinter) - موثوقة:
   ✅ بسيطة وسهلة الاستخدام
   ✅ تعمل على جميع الأنظمة
   ✅ حل مشكلة الأصفار
   ✅ خطوط كبيرة وواضحة
   ✅ أزرار تعمل بشكل صحيح
   ✅ لا تحتاج مكتبات إضافية

⚡ الواجهة المحسنة الأصلية:
   ✅ فلاتر بحث متقدمة
   ✅ شريط جانبي قابل للطي
   ✅ إحصائيات حية
   ✅ ألوان متناوبة
   ❌ أكثر تعقيداً

📋 الواجهة العادية:
   ✅ مستقرة ومجربة
   ✅ جميع الميزات الأساسية
   ❌ تصميم بسيط
   ❌ قد تواجه مشكلة الأصفار
"""
    
    print(comparison)

def main():
    """
    الدالة الرئيسية
    """
    # عرض المقارنة
    show_app_comparison()
    
    # السؤال عن الاختيار
    print("\n🤔 أي واجهة تريد تشغيلها؟")
    print("1️⃣  تشغيل أفضل واجهة متاحة تلقائياً (موصى به)")
    print("2️⃣  الواجهة الحديثة (PyQt5)")
    print("3️⃣  الواجهة المبسطة (tkinter)")
    print("4️⃣  الواجهة المحسنة الأصلية")
    print("5️⃣  الواجهة العادية")
    
    try:
        choice = input("\n👆 اختر رقم (1-5) أو اضغط Enter للاختيار التلقائي: ").strip()
        
        if choice == "2":
            print("🌟 تشغيل الواجهة الحديثة...")
            from modern_finance_app import run_modern_app
            run_modern_app()
        elif choice == "3":
            print("🔧 تشغيل الواجهة المبسطة...")
            from simple_finance_app import SimpleFinanceApp
            app = SimpleFinanceApp()
            app.run()
        elif choice == "4":
            print("⚡ تشغيل الواجهة المحسنة...")
            from enhanced_gui import run_enhanced_gui
            run_enhanced_gui()
        elif choice == "5":
            print("📋 تشغيل الواجهة العادية...")
            from gui_interface import FinanceGUI
            app = FinanceGUI()
            app.run()
        else:
            print("🎯 الاختيار التلقائي...")
            run_best_available_app()
            
    except KeyboardInterrupt:
        print("\n👋 تم إلغاء التشغيل")
    except Exception as e:
        print(f"\n❌ خطأ: {e}")
        print("🔄 محاولة التشغيل التلقائي...")
        run_best_available_app()

if __name__ == "__main__":
    main()
