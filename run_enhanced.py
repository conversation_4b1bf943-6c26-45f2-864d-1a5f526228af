#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل الواجهة المحسنة لبرنامج إدارة البيانات المالية
"""

import sys
import os
from enhanced_gui import run_enhanced_gui

def main():
    """
    تشغيل الواجهة المحسنة
    """
    print("=" * 60)
    print("🚀 برنامج إدارة البيانات المالية - الواجهة المحسنة")
    print("=" * 60)
    print("✨ الميزات الجديدة:")
    print("   🔍 فلاتر بحث متقدمة")
    print("   🎨 رؤوس أعمدة ملونة وتفاعلية")
    print("   📊 إحصائيات سريعة")
    print("   🔄 فرز ديناميكي للأعمدة")
    print("   📱 شريط جانبي قابل للطي")
    print("   🎯 تمرير أفقي وعمودي محسن")
    print("   💰 معالجة متقدمة للبيانات المالية")
    print("=" * 60)
    
    try:
        run_enhanced_gui()
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة المحسنة: {e}")
        print("🔄 جاري العودة للواجهة العادية...")
        
        # العودة للواجهة العادية في حالة الخطأ
        try:
            from gui_interface import FinanceGUI
            app = FinanceGUI()
            app.run()
        except Exception as e2:
            print(f"❌ خطأ في تشغيل الواجهة العادية أيضاً: {e2}")
            sys.exit(1)

if __name__ == "__main__":
    main()
