#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قالب Excel مثالي للبرنامج المالي
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def create_perfect_template():
    """
    إنشاء قالب Excel مثالي مع التنسيق والبيانات التجريبية
    """
    print("🎨 إنشاء قالب Excel مثالي للبرنامج المالي...")
    
    # البيانات التجريبية المثالية
    template_data = {
        'اسم الطالب': [
            'أحمد محمد علي السيد',
            'فاطمة حسن محمود أحمد', 
            'محمد عبدالله إبراهيم',
            'نور الهدى محمد حسن',
            'يوسف إبراهيم عبدالرحمن',
            'مريم عبدالله أحمد',
            'حسام الدين محمد علي',
            'سارة أحمد عبدالله',
            'عبدالرحمن يوسف محمد',
            'آية محمد حسن'
        ],
        'المرحلة': [
            'الابتدائية',
            'الإعدادية',
            'الثانوية', 
            'الابتدائية',
            'الإعدادية',
            'الثانوية',
            'الابتدائية',
            'الإعدادية',
            'الثانوية',
            'الابتدائية'
        ],
        'مديونية سابقة 23/24': [
            1000,
            2500,
            3000,
            0,
            1500,
            2000,
            500,
            1800,
            2200,
            0
        ],
        'مصروفات عام 2023/2024': [
            15000,
            18000,
            22000,
            15000,
            18000,
            22000,
            15000,
            18000,
            22000,
            15000
        ],
        'مصروفات دراسية 2025': [
            18000,
            20000,
            25000,
            18000,
            20000,
            25000,
            18000,
            20000,
            25000,
            18000
        ],
        'القسط الأول': [
            2000,
            3000,
            4000,
            2000,
            3000,
            4000,
            2000,
            3000,
            4000,
            2000
        ],
        'القسط الثاني': [
            2000,
            3000,
            4000,
            2000,
            3000,
            4000,
            2000,
            3000,
            4000,
            2000
        ],
        'القسط الثالث': [
            0,
            2000,
            3000,
            0,
            2000,
            3000,
            0,
            2000,
            3000,
            0
        ],
        'خصم إخوة': [
            500,
            1000,
            1500,
            500,
            1000,
            1500,
            500,
            1000,
            1500,
            500
        ],
        'خصم كاش': [
            0,
            200,
            300,
            0,
            200,
            300,
            0,
            200,
            300,
            0
        ],
        'ملاحظات': [
            'طالب متفوق أكاديمياً',
            'منتظم في الدفع',
            'يحتاج متابعة مالية',
            'طالب جديد',
            'حاصل على خصم تفوق',
            'متأخر في السداد',
            'طالب مثالي',
            'يحتاج دعم مالي',
            'متفوق رياضياً',
            'طالب منتظم'
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(template_data)
    
    # إنشاء Workbook مع التنسيق
    wb = Workbook()
    ws = wb.active
    ws.title = "البيانات المالية 2023-2024"
    
    # إضافة البيانات
    for r in dataframe_to_rows(df, index=False, header=True):
        ws.append(r)
    
    # تنسيق الرؤوس
    header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='4A90E2', end_color='4A90E2', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center')
    
    # تطبيق التنسيق على الرؤوس
    for cell in ws[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # تنسيق البيانات
    data_font = Font(name='Arial', size=11)
    data_alignment = Alignment(horizontal='center', vertical='center')
    
    # تطبيق التنسيق على البيانات
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
        for cell in row:
            cell.font = data_font
            cell.alignment = data_alignment
    
    # تعديل عرض الأعمدة
    column_widths = {
        'A': 25,  # اسم الطالب
        'B': 15,  # المرحلة
        'C': 20,  # مديونية سابقة
        'D': 22,  # مصروفات 2023/2024
        'E': 20,  # مصروفات 2025
        'F': 15,  # القسط الأول
        'G': 15,  # القسط الثاني
        'H': 15,  # القسط الثالث
        'I': 12,  # خصم إخوة
        'J': 12,  # خصم كاش
        'K': 25   # ملاحظات
    }
    
    for col, width in column_widths.items():
        ws.column_dimensions[col].width = width
    
    # إضافة حدود للجدول
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in ws.iter_rows(min_row=1, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
        for cell in row:
            cell.border = thin_border
    
    # حفظ الملف
    filename = 'قالب_البيانات_المالية_المثالي.xlsx'
    wb.save(filename)
    
    print(f"✅ تم إنشاء القالب المثالي: {filename}")
    print(f"📊 البيانات: {len(df)} صف و {len(df.columns)} عمود")
    
    return filename

def create_empty_template():
    """
    إنشاء قالب فارغ للملء اليدوي
    """
    print("\n📝 إنشاء قالب فارغ للملء اليدوي...")
    
    # الأعمدة المطلوبة
    columns = [
        'اسم الطالب',
        'المرحلة', 
        'مديونية سابقة 23/24',
        'مصروفات عام 2023/2024',
        'مصروفات دراسية 2025',
        'القسط الأول',
        'القسط الثاني', 
        'القسط الثالث',
        'خصم إخوة',
        'خصم كاش',
        'ملاحظات'
    ]
    
    # إنشاء DataFrame فارغ
    empty_df = pd.DataFrame(columns=columns)
    
    # إضافة صفوف فارغة للملء
    for i in range(20):  # 20 صف فارغ
        empty_df.loc[i] = [''] * len(columns)
    
    # إنشاء Workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "قالب فارغ للملء"
    
    # إضافة الرؤوس
    for col_num, column_title in enumerate(columns, 1):
        cell = ws.cell(row=1, column=col_num)
        cell.value = column_title
        
        # تنسيق الرؤوس
        cell.font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        cell.fill = PatternFill(start_color='4A90E2', end_color='4A90E2', fill_type='solid')
        cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # إضافة الصفوف الفارغة
    for row_num in range(2, 22):  # 20 صف فارغ
        for col_num in range(1, len(columns) + 1):
            cell = ws.cell(row=row_num, column=col_num)
            cell.value = ""
            cell.font = Font(name='Arial', size=11)
            cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # تعديل عرض الأعمدة
    column_widths = [25, 15, 20, 22, 20, 15, 15, 15, 12, 12, 25]
    for i, width in enumerate(column_widths, 1):
        ws.column_dimensions[ws.cell(row=1, column=i).column_letter].width = width
    
    # إضافة حدود
    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'), 
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for row in ws.iter_rows(min_row=1, max_row=21, min_col=1, max_col=len(columns)):
        for cell in row:
            cell.border = thin_border
    
    # حفظ الملف
    filename = 'قالب_فارغ_للملء_اليدوي.xlsx'
    wb.save(filename)
    
    print(f"✅ تم إنشاء القالب الفارغ: {filename}")
    print(f"📝 جاهز لإدخال {20} طالب")
    
    return filename

def create_instructions_file():
    """
    إنشاء ملف تعليمات الاستخدام
    """
    print("\n📋 إنشاء ملف تعليمات الاستخدام...")
    
    instructions = """
🎯 تعليمات استخدام القوالب المالية

📁 الملفات المتوفرة:
1. قالب_البيانات_المالية_المثالي.xlsx - قالب مع بيانات تجريبية
2. قالب_فارغ_للملء_اليدوي.xlsx - قالب فارغ للملء اليدوي

📝 طريقة الملء الصحيحة:

🔹 اسم الطالب:
   - اكتب الاسم كاملاً (مثال: أحمد محمد علي السيد)
   - لا تترك الخلية فارغة

🔹 المرحلة:
   - استخدم فقط: الابتدائية، الإعدادية، الثانوية
   - لا تستخدم اختصارات

🔹 الأرقام المالية:
   - اكتب الأرقام بدون فواصل (مثال: 15000 وليس 15,000)
   - استخدم 0 للقيم الصفرية
   - لا تترك خلايا فارغة في الأعمدة المالية

🔹 الملاحظات:
   - يمكن ترك هذا العمود فارغاً
   - أو كتابة ملاحظات مفيدة

⚠️ تجنب هذه الأخطاء:
❌ ترك خلايا فارغة في الأعمدة المالية
❌ استخدام فواصل في الأرقام (15,000)
❌ كتابة كلمات في الأعمدة الرقمية
❌ استخدام أسماء مراحل غير صحيحة

✅ مثال صحيح:
اسم الطالب: أحمد محمد علي
المرحلة: الابتدائية
مديونية سابقة: 1000
مصروفات 2023: 15000
القسط الأول: 2000

🚀 بعد الملء:
1. احفظ الملف
2. افتح البرنامج
3. انقر "استيراد Excel"
4. اختر الملف المملوء
5. ستتم معالجة البيانات تلقائياً

💡 نصائح:
- استخدم القالب المثالي كمرجع
- املأ القالب الفارغ ببياناتك الحقيقية
- تأكد من صحة البيانات قبل الاستيراد
"""
    
    with open('تعليمات_استخدام_القوالب.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ تم إنشاء ملف التعليمات: تعليمات_استخدام_القوالب.txt")

def main():
    """
    الدالة الرئيسية
    """
    print("🎨 إنشاء القوالب المثالية للبرنامج المالي")
    print("=" * 60)
    
    # إنشاء القوالب
    perfect_template = create_perfect_template()
    empty_template = create_empty_template()
    create_instructions_file()
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء جميع القوالب بنجاح!")
    print("=" * 60)
    
    print(f"\n📁 الملفات المنشأة:")
    print(f"   🎯 {perfect_template} - قالب مع بيانات تجريبية")
    print(f"   📝 {empty_template} - قالب فارغ للملء")
    print(f"   📋 تعليمات_استخدام_القوالب.txt - دليل الاستخدام")
    
    print(f"\n💡 الخطوات التالية:")
    print(f"   1️⃣ افتح القالب المثالي لرؤية المثال")
    print(f"   2️⃣ استخدم القالب الفارغ لإدخال بياناتك")
    print(f"   3️⃣ اقرأ ملف التعليمات للإرشادات")
    print(f"   4️⃣ استورد الملف في البرنامج")
    print(f"   5️⃣ استخدم ميزة النقل للسنة الجديدة")

if __name__ == "__main__":
    main()
