#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف Excel مثال لاختبار البرنامج
"""

import pandas as pd
from datetime import datetime, timedelta
import random

def create_sample_data():
    """
    إنشاء بيانات مثال للطلاب
    """
    # أسماء طلاب مثال
    student_names = [
        "أحمد محمد علي",
        "فاطمة أحمد حسن",
        "محمد عبدالله سالم",
        "عائشة محمود إبراهيم",
        "عمر خالد يوسف",
        "زينب عبدالرحمن أحمد",
        "يوسف محمد عبدالله",
        "مريم سعد محمد",
        "حسام علي حسن",
        "نور الدين محمد علي",
        "سارة أحمد محمود",
        "كريم عبدالله خالد",
        "هدى محمد سالم",
        "طارق علي أحمد",
        "ليلى حسن محمود"
    ]

    # المراحل الدراسية
    stages = ["رياض الأطفال", "الابتدائية", "الإعدادية", "الثانوية"]

    # إنشاء البيانات
    data = []

    for i, name in enumerate(student_names, 1):
        stage = random.choice(stages)

        # تحديد الرسوم حسب المرحلة
        fees_map = {
            "رياض الأطفال": 15000,
            "الابتدائية": 18000,
            "الإعدادية": 20000,
            "الثانوية": 25000
        }

        old_fees = fees_map[stage]
        new_fees = old_fees * 1.1  # زيادة 10%

        # مديونية سابقة عشوائية
        previous_debt = random.choice([0, 1000, 2000, 3000, 5000])

        # أقساط مدفوعة عشوائية
        installments = []
        total_paid = 0
        for j in range(9):
            if j < 3:  # أول 3 أقساط مدفوعة غالباً
                amount = random.choice([0, 2000, 2500, 3000])
            elif j < 6:  # الأقساط الوسطى
                amount = random.choice([0, 0, 1500, 2000])
            else:  # الأقساط الأخيرة
                amount = random.choice([0, 0, 0, 1000])

            installments.append(amount)
            total_paid += amount

        # خصومات عشوائية
        sibling_discount = random.choice([0, 500, 1000]) if random.random() > 0.7 else 0
        cash_discount = random.choice([0, 200, 500]) if random.random() > 0.8 else 0
        teacher_discount = random.choice([0, 1000, 2000]) if random.random() > 0.9 else 0

        total_discounts = sibling_discount + cash_discount + teacher_discount

        # حساب المتبقي
        remaining = (new_fees + previous_debt) - (total_paid + total_discounts)

        # تواريخ الأقساط
        base_date = datetime(2024, 2, 1)
        installment_dates = []
        for j in range(9):
            if installments[j] > 0:
                date = base_date + timedelta(days=30*j + random.randint(0, 15))
                installment_dates.append(date.strftime("%Y-%m-%d"))
            else:
                installment_dates.append("")

        # إنشاء السجل بالأعمدة الصحيحة تماماً
        record = {
            'العدد الإجمالي': i,
            'السيريال': f'S{i:03d}',
            'رسم فتح ملف': 500,
            'تاريخ فتح الملف': '2024-01-15',
            'فتح ملف مرحل': 0,
            'تاريخ فتح الملف المرحل': '',
            'الدفعة': f'دفعة 2024-{random.choice(["أ", "ب", "ج"])}',
            'المرحلة': stage,
            'اسم الطالب': name,
            'مديونية سابقة 23/24': previous_debt,
            'مصروفات عام 2023/2024': old_fees,
            'مصروفات دراسية 2025': new_fees,
            'القسط الأول': installments[0],
            'تاريخ القسط الأول': installment_dates[0],
            'رقم قيد القسط الأول': f'R{i:03d}-1' if installments[0] > 0 else '',
            'القسط الثاني': installments[1],
            'تاريخ القسط الثاني': installment_dates[1],
            'رقم قيد القسط الثاني': f'R{i:03d}-2' if installments[1] > 0 else '',
            'القسط الثالث': installments[2],
            'تاريخ القسط الثالث': installment_dates[2],
            'رقم قيد القسط الثالث': f'R{i:03d}-3' if installments[2] > 0 else '',
            'القسط الرابع': installments[3],
            'تاريخ القسط الرابع': installment_dates[3],
            'رقم قيد القسط الرابع': f'R{i:03d}-4' if installments[3] > 0 else '',
            'القسط الخامس': installments[4],
            'تاريخ القسط الخامس': installment_dates[4],
            'رقم قيد القسط الخامس': f'R{i:03d}-5' if installments[4] > 0 else '',
            'القسط السادس': installments[5],
            'تاريخ القسط السادس': installment_dates[5],
            'رقم قيد القسط السادس': f'R{i:03d}-6' if installments[5] > 0 else '',
            'القسط السابع': installments[6],
            'تاريخ القسط السابع': installment_dates[6],
            'رقم قيد القسط السابع': f'R{i:03d}-7' if installments[6] > 0 else '',
            'القسط الثامن': installments[7],
            'تاريخ القسط الثامن': installment_dates[7],
            'رقم قيد القسط الثامن': f'R{i:03d}-8' if installments[7] > 0 else '',
            'القسط التاسع': installments[8],
            'تاريخ القسط التاسع': installment_dates[8],
            'رقم قيد القسط التاسع': f'R{i:03d}-9' if installments[8] > 0 else '',
            'إجمالي الأقساط المدفوعة': total_paid,
            'المتبقي': remaining,
            'خصم إخوة': sibling_discount,
            'خصم كاش': cash_discount,
            'خصم مدرسين': teacher_discount,
            'خصم تحويل': 0,
            'خصم شخصي': 0,
            'إجمالي الخصومات': total_discounts,
            'ملاحظات': f'طالب في {stage}' + (' - يوجد خصم إخوة' if sibling_discount > 0 else '')
        }

        data.append(record)

    return pd.DataFrame(data)

def main():
    """
    إنشاء وحفظ ملف Excel مثال
    """
    print("جاري إنشاء بيانات مثال...")

    # إنشاء البيانات
    df = create_sample_data()

    # حفظ الملف
    filename = "sample_students_data.xlsx"

    try:
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='بيانات الطلاب', index=False)

            # تنسيق الملف
            worksheet = writer.sheets['بيانات الطلاب']

            # ضبط عرض الأعمدة
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter

                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass

                adjusted_width = min(max_length + 2, 30)
                worksheet.column_dimensions[column_letter].width = adjusted_width

        print(f"✓ تم إنشاء ملف البيانات المثال: {filename}")
        print(f"✓ عدد الطلاب: {len(df)}")
        print("✓ يمكنك الآن استخدام هذا الملف لاختبار البرنامج")

    except Exception as e:
        print(f"✗ خطأ في إنشاء الملف: {e}")

if __name__ == "__main__":
    main()
