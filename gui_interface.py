# -*- coding: utf-8 -*-
"""
واجهة المستخدم الرسومية
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import pandas as pd
from typing import Optional, List, Dict, Any
from database_manager import DatabaseManager
from data_processor import DataProcessor
from excel_handler import ExcelHandler
from config import WINDOW_WIDTH_RATIO, WINDOW_HEIGHT_RATIO, STANDARD_COLUMNS, ACADEMIC_STAGES

class FinanceGUI:
    def __init__(self):
        """
        تهيئة واجهة المستخدم الرسومية
        """
        self.root = tk.Tk()
        self.root.title("برنامج إدارة البيانات المالية للطلاب")
        self.root.state('zoomed')  # تكبير النافذة لتشغل الشاشة كاملة

        # تهيئة المكونات
        self.db_manager = DatabaseManager()
        self.data_processor = DataProcessor(self.db_manager)
        self.excel_handler = ExcelHandler()

        # متغيرات البيانات
        self.current_data = pd.DataFrame()

        # إعداد الواجهة
        self.setup_ui()
        self.load_data()

    def setup_enhanced_styles(self, style):
        """
        إعداد الأنماط المحسنة مع خطوط أكبر وإصلاح مشكلة الهوفر
        """
        # نمط رؤوس الأعمدة - مع إصلاح مشكلة الهوفر
        style.configure(
            "Treeview.Heading",
            background='#4a90e2',
            foreground='white',
            font=('Arial', 12, 'bold'),  # خط أكبر
            relief='raised',
            borderwidth=1,
            focuscolor='none'  # إزالة لون التركيز
        )

        # إصلاح مشكلة الهوفر - الحفاظ على لون النص الأبيض
        style.map("Treeview.Heading",
                 background=[('active', '#3a7bc8'),  # لون أغمق عند الهوفر
                           ('pressed', '#2a6bb8')],   # لون أغمق عند الضغط
                 foreground=[('active', 'white'),     # نص أبيض عند الهوفر
                           ('pressed', 'white'),      # نص أبيض عند الضغط
                           ('!active', 'white')])     # نص أبيض في الحالة العادية

        # نمط الجدول - خط أكبر
        style.configure(
            "Treeview",
            background='white',
            foreground='black',
            rowheight=30,  # ارتفاع أكبر للصفوف
            fieldbackground='white',
            font=('Arial', 11)  # خط أكبر للبيانات
        )

        # نمط الصفوف المتناوبة
        style.map("Treeview",
                 background=[('selected', '#0078d4')],
                 foreground=[('selected', 'white')])

        # نمط الأزرار المميزة - خط أكبر
        style.configure(
            "Accent.TButton",
            background='#0078d4',
            foreground='white',
            font=('Arial', 11, 'bold')  # خط أكبر
        )

        # نمط الأزرار العادية - خط أكبر
        style.configure(
            "TButton",
            font=('Arial', 10)  # خط أكبر للأزرار العادية
        )

        # نمط التسميات - خط أكبر
        style.configure(
            "TLabel",
            font=('Arial', 10)  # خط أكبر للتسميات
        )

        # نمط حقول الإدخال - خط أكبر
        style.configure(
            "TEntry",
            font=('Arial', 11),  # خط أكبر لحقول الإدخال
            fieldbackground='white'
        )

        # نمط القوائم المنسدلة - خط أكبر
        style.configure(
            "TCombobox",
            font=('Arial', 10)  # خط أكبر للقوائم المنسدلة
        )

    def setup_ui(self):
        """
        إعداد واجهة المستخدم
        """
        # إعداد النمط المحسن
        style = ttk.Style()
        style.theme_use('clam')

        # تحسين أنماط الخطوط والألوان
        self.setup_enhanced_styles(style)

        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط الأدوات العلوي
        self.create_toolbar(main_frame)

        # منطقة عرض البيانات
        self.create_data_display(main_frame)

        # شريط الحالة السفلي
        self.create_status_bar(main_frame)

    def create_toolbar(self, parent):
        """
        إنشاء شريط الأدوات
        """
        toolbar_frame = ttk.Frame(parent)
        toolbar_frame.pack(fill=tk.X, pady=(0, 10))

        # الأزرار الرئيسية
        ttk.Button(
            toolbar_frame,
            text="استيراد ملف Excel",
            command=self.import_excel,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="تصدير إلى Excel",
            command=self.export_excel,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="إنشاء قالب",
            command=self.create_template,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        # فاصل
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        ttk.Button(
            toolbar_frame,
            text="إضافة طالب",
            command=self.add_student,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="تحرير طالب",
            command=self.edit_student,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="حذف طالب",
            command=self.delete_student,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        # فاصل
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        ttk.Button(
            toolbar_frame,
            text="إدارة الرسوم",
            command=self.manage_fees,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        # فاصل
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=10)

        ttk.Button(
            toolbar_frame,
            text="نقل للسنة الجديدة",
            command=self.transfer_to_new_year,
            width=18,
            style='Accent.TButton'
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            toolbar_frame,
            text="الإحصائيات",
            command=self.show_statistics,
            width=15
        ).pack(side=tk.LEFT, padx=(0, 5))

        # زر التحديث على اليمين
        ttk.Button(
            toolbar_frame,
            text="تحديث البيانات",
            command=self.refresh_data,
            width=15
        ).pack(side=tk.RIGHT)

    def create_data_display(self, parent):
        """
        إنشاء منطقة عرض البيانات
        """
        # إطار الجدول
        self.table_frame = ttk.Frame(parent)
        self.table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview للجدول - سيتم تحديث الأعمدة ديناميكياً
        columns = STANDARD_COLUMNS  # البداية بالأعمدة الافتراضية
        self.tree = ttk.Treeview(self.table_frame, columns=columns, show='headings', height=20)

        # تعيين رؤوس الأعمدة
        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER)
            self.tree.column(col, width=120, anchor=tk.CENTER)

        # أشرطة التمرير
        self.v_scrollbar = ttk.Scrollbar(self.table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.h_scrollbar = ttk.Scrollbar(self.table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)

        self.tree.configure(yscrollcommand=self.v_scrollbar.set, xscrollcommand=self.h_scrollbar.set)

        # ترتيب العناصر
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)

    def create_status_bar(self, parent):
        """
        إنشاء شريط الحالة
        """
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_label = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.count_label = ttk.Label(status_frame, text="عدد الطلاب: 0", relief=tk.SUNKEN)
        self.count_label.pack(side=tk.RIGHT, padx=(10, 0))

    def update_status(self, message: str):
        """
        تحديث شريط الحالة
        """
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def update_count(self, count: int):
        """
        تحديث عداد الطلاب
        """
        self.count_label.config(text=f"عدد الطلاب: {count}")

    def load_data(self):
        """
        تحميل البيانات من قاعدة البيانات
        """
        try:
            self.update_status("جاري تحميل البيانات...")
            self.current_data = self.db_manager.get_all_students()
            self.populate_tree()
            self.update_status("تم تحميل البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
            self.update_status("خطأ في تحميل البيانات")

    def populate_tree(self):
        """
        ملء الجدول بالبيانات
        """
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        if self.current_data.empty:
            self.update_count(0)
            return

        # إضافة البيانات الجديدة
        columns = list(self.tree['columns'])

        for _, row in self.current_data.iterrows():
            values = []
            for col in columns:
                # تحويل أسماء الأعمدة من قاعدة البيانات إلى الأسماء المعروضة
                db_col = self.get_db_column_name(col)
                if db_col in self.current_data.columns:
                    value = row[db_col]
                    if pd.isna(value) or value is None:
                        # التعامل مع القيم الفارغة حسب نوع العمود
                        if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية', 'المتبقي']):
                            values.append('0')  # عرض صفر للأعمدة المالية
                        else:
                            values.append('غير محدد')  # عرض "غير محدد" للأعمدة النصية
                    else:
                        values.append(str(value))
                else:
                    # العمود غير موجود في البيانات
                    if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية', 'المتبقي']):
                        values.append('0')  # عرض صفر للأعمدة المالية المفقودة
                    else:
                        values.append('غير محدد')

            self.tree.insert('', tk.END, values=values)

        self.update_count(len(self.current_data))

    def get_db_column_name(self, display_name: str) -> str:
        """
        تحويل اسم العمود المعروض إلى اسم العمود في قاعدة البيانات
        """
        mapping = {
            'العدد الإجمالي': 'total_count',
            'السيريال': 'serial_number',
            'رسم فتح ملف': 'file_opening_fee',
            'تاريخ فتح الملف': 'file_opening_date',
            'فتح ملف مرحل': 'transferred_file_opening',
            'تاريخ فتح الملف المرحل': 'transferred_file_date',
            'الدفعة': 'batch_number',
            'المرحلة': 'academic_stage',
            'اسم الطالب': 'student_name',
            'مديونية سابقة 23/24': 'previous_debt_2324',
            'مصروفات عام 2023/2024': 'old_year_fees_2324',
            'مصروفات دراسية 2025': 'new_year_fees_2025',
            'القسط الأول': 'installment_1',
            'القسط الثاني': 'installment_2',
            'القسط الثالث': 'installment_3',
            'القسط الرابع': 'installment_4',
            'القسط الخامس': 'installment_5',
            'القسط السادس': 'installment_6',
            'القسط السابع': 'installment_7',
            'القسط الثامن': 'installment_8',
            'القسط التاسع': 'installment_9',
            'تاريخ القسط الأول': 'installment_1_date',
            'تاريخ القسط الثاني': 'installment_2_date',
            'تاريخ القسط الثالث': 'installment_3_date',
            'تاريخ القسط الرابع': 'installment_4_date',
            'تاريخ القسط الخامس': 'installment_5_date',
            'تاريخ القسط السادس': 'installment_6_date',
            'تاريخ القسط السابع': 'installment_7_date',
            'تاريخ القسط الثامن': 'installment_8_date',
            'تاريخ القسط التاسع': 'installment_9_date',
            'رقم قيد القسط الأول': 'installment_1_receipt',
            'رقم قيد القسط الثاني': 'installment_2_receipt',
            'رقم قيد القسط الثالث': 'installment_3_receipt',
            'رقم قيد القسط الرابع': 'installment_4_receipt',
            'رقم قيد القسط الخامس': 'installment_5_receipt',
            'رقم قيد القسط السادس': 'installment_6_receipt',
            'رقم قيد القسط السابع': 'installment_7_receipt',
            'رقم قيد القسط الثامن': 'installment_8_receipt',
            'رقم قيد القسط التاسع': 'installment_9_receipt',
            'إجمالي الأقساط المدفوعة': 'total_paid',
            'المتبقي': 'remaining_balance',
            'خصم إخوة': 'sibling_discount',
            'خصم كاش': 'cash_discount',
            'خصم مدرسين': 'teacher_discount',
            'خصم تحويل': 'transfer_discount',
            'خصم شخصي': 'personal_discount',
            'إجمالي الخصومات': 'total_discounts',
            'ملاحظات': 'notes'
        }
        return mapping.get(display_name, display_name.lower().replace(' ', '_'))

    def import_excel(self):
        """
        استيراد ملف Excel مع المعالجة المحسنة
        """
        try:
            self.update_status("جاري استيراد ملف Excel...")

            # استيراد الملف مع المعالجة المحسنة
            df, warnings = self.excel_handler.import_excel_file()

            if df.empty:
                messagebox.showwarning("تحذير", "الملف فارغ أو لا يحتوي على بيانات صالحة")
                self.update_status("الملف فارغ")
                return

            # عرض تحذيرات إن وجدت
            if warnings:
                warning_msg = "تم استيراد الملف مع التحذيرات التالية:\n\n" + "\n".join(warnings[:10])
                if len(warnings) > 10:
                    warning_msg += f"\n... و {len(warnings) - 10} تحذير إضافي"

                if not messagebox.askyesno("تحذيرات في البيانات",
                                         f"{warning_msg}\n\nهل تريد المتابعة؟"):
                    self.update_status("تم إلغاء الاستيراد")
                    return

            # معالجة البيانات
            processed_df, processing_errors = self.data_processor.process_excel_data(df)

            if processing_errors:
                error_msg = "\n".join(processing_errors)
                if not messagebox.askyesno("أخطاء في المعالجة",
                                         f"تم العثور على الأخطاء التالية:\n{error_msg}\n\nهل تريد المتابعة؟"):
                    self.update_status("تم إلغاء الاستيراد")
                    return

            # تحضير البيانات للإدراج في قاعدة البيانات
            records = self.data_processor.prepare_for_database(processed_df)

            # حذف البيانات القديمة (اختياري)
            if messagebox.askyesno("تأكيد", "هل تريد حذف البيانات الموجودة واستبدالها بالبيانات الجديدة؟"):
                self.db_manager.clear_all_students()

            # إدراج البيانات الجديدة
            success_count = 0
            for record in records:
                try:
                    self.db_manager.insert_student_data(record)
                    success_count += 1
                except Exception as e:
                    print(f"خطأ في إدراج السجل: {e}")

            # تحديث العرض
            self.load_data()

            # رسالة النجاح مع التفاصيل
            success_msg = f"✅ تم استيراد {success_count} سجل بنجاح\n\n"
            success_msg += f"📊 إجمالي الصفوف: {len(df)}\n"
            success_msg += f"📋 إجمالي الأعمدة: {len(df.columns)}\n"

            if warnings:
                success_msg += f"⚠️ تحذيرات: {len(warnings)}\n"

            # إحصائيات البيانات المالية
            financial_cols = [col for col in df.columns if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية'])]
            if financial_cols:
                success_msg += f"💰 الأعمدة المالية: {len(financial_cols)}"

            messagebox.showinfo("نجح الاستيراد", success_msg)
            self.update_status(f"تم استيراد {success_count} سجل مع معالجة محسنة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد الملف: {str(e)}")
            self.update_status("خطأ في الاستيراد")

    def export_excel(self):
        """
        تصدير البيانات إلى Excel
        """
        try:
            if self.current_data.empty:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            self.update_status("جاري تصدير البيانات...")

            # تحويل أسماء الأعمدة إلى الأسماء المعروضة
            display_data = self.current_data.copy()

            # تصدير الملف
            success, message = self.excel_handler.export_to_excel(display_data)

            if success:
                messagebox.showinfo("نجح التصدير", message)
                self.update_status("تم التصدير بنجاح")
            else:
                messagebox.showerror("خطأ في التصدير", message)
                self.update_status("فشل في التصدير")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف: {str(e)}")
            self.update_status("خطأ في التصدير")

    def create_template(self):
        """
        إنشاء قالب Excel
        """
        try:
            self.update_status("جاري إنشاء قالب Excel...")

            success, message = self.excel_handler.create_template_file()

            if success:
                messagebox.showinfo("نجح إنشاء القالب", message)
                self.update_status("تم إنشاء القالب بنجاح")
            else:
                messagebox.showerror("خطأ", message)
                self.update_status("فشل في إنشاء القالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء القالب: {str(e)}")
            self.update_status("خطأ في إنشاء القالب")

    def refresh_data(self):
        """
        تحديث البيانات
        """
        self.load_data()

    def on_item_double_click(self, event):
        """
        معالج النقر المزدوج على عنصر في الجدول
        """
        self.edit_student()

    def add_student(self):
        """
        إضافة طالب جديد
        """
        messagebox.showinfo("قريباً", "ميزة إضافة طالب جديد ستكون متاحة قريباً")

    def edit_student(self):
        """
        تحرير بيانات طالب
        """
        messagebox.showinfo("قريباً", "ميزة تحرير بيانات الطالب ستكون متاحة قريباً")

    def delete_student(self):
        """
        حذف طالب
        """
        messagebox.showinfo("قريباً", "ميزة حذف الطالب ستكون متاحة قريباً")

    def manage_fees(self):
        """
        إدارة الرسوم - نافذة لتحديد الرسوم الجديدة لكل مرحلة
        """
        self.open_fee_management_window()

    def show_statistics(self):
        """
        عرض الإحصائيات
        """
        try:
            stats = self.db_manager.get_statistics()

            stats_window = tk.Toplevel(self.root)
            stats_window.title("الإحصائيات")
            stats_window.geometry("600x400")

            # إطار النص
            text_frame = ttk.Frame(stats_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # منطقة النص
            text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('Arial', 12))
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            # إضافة الإحصائيات
            stats_text = f"""الإحصائيات العامة:

إجمالي عدد الطلاب: {stats['total_students']}
إجمالي المديونيات: {stats['total_debt']:,.2f} جنيه
إجمالي الأقساط المدفوعة: {stats['total_paid']:,.2f} جنيه

الإحصائيات حسب المرحلة:
"""

            for stage_stat in stats['stage_statistics']:
                stage, count, debt, paid = stage_stat
                stats_text += f"\n{stage}: {count} طالب - مديونية: {debt:,.2f} - مدفوع: {paid:,.2f}"

            text_widget.insert(tk.END, stats_text)
            text_widget.config(state=tk.DISABLED)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض الإحصائيات: {str(e)}")

    def open_fee_management_window(self):
        """
        فتح نافذة إدارة الرسوم
        """
        fee_window = tk.Toplevel(self.root)
        fee_window.title("إدارة الرسوم الدراسية للسنة المالية الجديدة")
        fee_window.geometry("600x500")
        fee_window.resizable(True, True)

        # جعل النافذة في المقدمة
        fee_window.transient(self.root)
        fee_window.grab_set()

        # الإطار الرئيسي
        main_frame = ttk.Frame(fee_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # العنوان
        title_label = ttk.Label(
            main_frame,
            text="تحديد الرسوم الدراسية للسنة المالية الجديدة 2025",
            font=('Arial', 14, 'bold')
        )
        title_label.pack(pady=(0, 20))

        # تعليمات
        instructions = ttk.Label(
            main_frame,
            text="أدخل الرسوم الجديدة لكل مرحلة دراسية (بالجنيه المصري)",
            font=('Arial', 10)
        )
        instructions.pack(pady=(0, 15))

        # إطار الرسوم
        fees_frame = ttk.LabelFrame(main_frame, text="الرسوم الدراسية", padding="15")
        fees_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # متغيرات الرسوم
        self.fee_vars = {}

        # الحصول على الرسوم الحالية
        current_fees = self.db_manager.get_fee_structure()
        fee_dict = {}
        if not current_fees.empty:
            fee_dict = dict(zip(current_fees['academic_stage'], current_fees['tuition_fees']))

        # إنشاء حقول الإدخال لكل مرحلة
        stages = ['رياض الأطفال', 'الابتدائية', 'الإعدادية', 'الثانوية']

        for i, stage in enumerate(stages):
            # إطار كل مرحلة
            stage_frame = ttk.Frame(fees_frame)
            stage_frame.pack(fill=tk.X, pady=5)

            # تسمية المرحلة
            stage_label = ttk.Label(stage_frame, text=f"{stage}:", width=15, anchor='e')
            stage_label.pack(side=tk.LEFT, padx=(0, 10))

            # حقل الإدخال
            fee_var = tk.StringVar()
            current_fee = fee_dict.get(stage, 0)
            fee_var.set(str(int(current_fee)) if current_fee else "")

            fee_entry = ttk.Entry(stage_frame, textvariable=fee_var, width=15, font=('Arial', 11))
            fee_entry.pack(side=tk.LEFT, padx=(0, 10))

            # تسمية العملة
            currency_label = ttk.Label(stage_frame, text="جنيه", width=8)
            currency_label.pack(side=tk.LEFT)

            self.fee_vars[stage] = fee_var

        # فاصل
        ttk.Separator(main_frame, orient=tk.HORIZONTAL).pack(fill=tk.X, pady=15)

        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)

        # زر الحفظ
        save_button = ttk.Button(
            buttons_frame,
            text="حفظ الرسوم الجديدة",
            command=lambda: self.save_new_fees(fee_window),
            style='Accent.TButton'
        )
        save_button.pack(side=tk.LEFT, padx=(0, 10))

        # زر الإلغاء
        cancel_button = ttk.Button(
            buttons_frame,
            text="إلغاء",
            command=fee_window.destroy
        )
        cancel_button.pack(side=tk.LEFT, padx=(0, 10))

        # زر استعادة الافتراضي
        default_button = ttk.Button(
            buttons_frame,
            text="القيم الافتراضية",
            command=self.load_default_fees
        )
        default_button.pack(side=tk.RIGHT)

        # معلومات إضافية
        info_frame = ttk.LabelFrame(main_frame, text="معلومات", padding="10")
        info_frame.pack(fill=tk.X, pady=(15, 0))

        info_text = """
• سيتم تطبيق هذه الرسوم على جميع الطلاب في المرحلة المحددة
• الرسوم الجديدة ستستخدم لحساب المديونية للسنة المالية 2025
• يمكن تعديل الرسوم في أي وقت قبل نقل البيانات
        """

        info_label = ttk.Label(info_frame, text=info_text, font=('Arial', 9), foreground='gray')
        info_label.pack()

    def load_default_fees(self):
        """
        تحميل القيم الافتراضية للرسوم
        """
        default_fees = {
            'رياض الأطفال': '15000',
            'الابتدائية': '18000',
            'الإعدادية': '20000',
            'الثانوية': '25000'
        }

        for stage, fee in default_fees.items():
            if stage in self.fee_vars:
                self.fee_vars[stage].set(fee)

    def save_new_fees(self, window):
        """
        حفظ الرسوم الجديدة
        """
        try:
            # التحقق من صحة البيانات
            fees_to_save = {}
            errors = []

            for stage, fee_var in self.fee_vars.items():
                fee_text = fee_var.get().strip()

                if not fee_text:
                    errors.append(f"لم يتم إدخال رسوم {stage}")
                    continue

                try:
                    fee_amount = float(fee_text)
                    if fee_amount < 0:
                        errors.append(f"رسوم {stage} لا يمكن أن تكون سالبة")
                        continue

                    fees_to_save[stage] = fee_amount

                except ValueError:
                    errors.append(f"رسوم {stage} يجب أن تكون رقم صحيح")

            if errors:
                messagebox.showerror("خطأ في البيانات", "\n".join(errors))
                return

            # حفظ الرسوم في قاعدة البيانات
            success_count = 0
            for stage, fee in fees_to_save.items():
                if self.db_manager.update_fee_structure(stage, fee, 500):  # رسم فتح ملف افتراضي 500
                    success_count += 1

            if success_count == len(fees_to_save):
                messagebox.showinfo(
                    "تم الحفظ بنجاح",
                    f"تم حفظ الرسوم الجديدة لـ {success_count} مرحلة دراسية\n\n"
                    "يمكنك الآن استخدام هذه الرسوم لنقل البيانات إلى السنة المالية الجديدة"
                )
                window.destroy()
                self.update_status("تم حفظ الرسوم الجديدة")
            else:
                messagebox.showerror("خطأ", "حدث خطأ في حفظ بعض الرسوم")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الرسوم: {str(e)}")

    def transfer_to_new_year(self):
        """
        نقل البيانات من السنة المالية القديمة إلى السنة الجديدة
        """
        try:
            # التحقق من وجود رسوم محددة للسنة الجديدة
            fee_structure = self.db_manager.get_fee_structure()
            if fee_structure.empty:
                messagebox.showwarning(
                    "رسوم غير محددة",
                    "يجب تحديد الرسوم الدراسية للسنة الجديدة أولاً.\n\nانقر على 'إدارة الرسوم' لتحديد الرسوم."
                )
                return

            # التحقق من وجود بيانات للنقل
            if self.current_data.empty:
                messagebox.showwarning(
                    "لا توجد بيانات",
                    "يجب استيراد بيانات السنة المالية القديمة أولاً.\n\nانقر على 'استيراد ملف Excel' لتحميل البيانات."
                )
                return

            # تأكيد العملية
            confirm = messagebox.askyesno(
                "تأكيد نقل البيانات",
                f"سيتم نقل بيانات {len(self.current_data)} طالب إلى السنة المالية الجديدة 2025.\n\n"
                "سيتم:\n"
                "• تطبيق الرسوم الجديدة حسب كل مرحلة\n"
                "• الاحتفاظ بالمديونيات السابقة والأقساط المدفوعة\n"
                "• حساب الرصيد الجديد لكل طالب\n"
                "• مسح الأقساط الجديدة (تبدأ من صفر)\n\n"
                "هل تريد المتابعة؟"
            )

            if not confirm:
                return

            self.update_status("جاري نقل البيانات للسنة الجديدة...")

            # إنشاء قاموس الرسوم الجديدة
            fee_dict = dict(zip(fee_structure['academic_stage'], fee_structure['tuition_fees']))

            # معالجة كل طالب
            updated_records = []

            for _, student in self.current_data.iterrows():
                # الحصول على بيانات الطالب
                stage = student.get('academic_stage', '')
                previous_debt = float(student.get('previous_debt_2324', 0))

                # حساب إجمالي الأقساط المدفوعة
                total_paid = 0
                for i in range(1, 10):
                    installment = float(student.get(f'installment_{i}', 0))
                    total_paid += installment

                # حساب إجمالي الخصومات
                total_discounts = 0
                discount_columns = ['sibling_discount', 'cash_discount', 'teacher_discount', 'transfer_discount', 'personal_discount']
                for col in discount_columns:
                    discount = float(student.get(col, 0))
                    total_discounts += discount

                # الحصول على الرسوم الجديدة للمرحلة
                new_fees = fee_dict.get(stage, 0)

                # حساب الرصيد الجديد
                # الرصيد الجديد = (الرسوم الجديدة + المديونية السابقة) - (الأقساط المدفوعة + الخصومات)
                new_balance = (new_fees + previous_debt) - (total_paid + total_discounts)

                # إنشاء السجل الجديد
                new_record = {
                    'student_name': student.get('student_name', ''),
                    'academic_stage': stage,
                    'previous_debt_2324': new_balance,  # المديونية الجديدة = الرصيد المحسوب
                    'old_year_fees_2324': student.get('old_year_fees_2324', 0),  # الاحتفاظ بالرسوم القديمة للمرجع
                    'new_year_fees_2025': new_fees,  # الرسوم الجديدة
                    'total_paid': 0,  # مسح الأقساط المدفوعة (تبدأ من جديد)
                    'remaining_balance': new_balance,  # الرصيد المتبقي
                    'sibling_discount': student.get('sibling_discount', 0),  # الاحتفاظ بالخصومات
                    'cash_discount': student.get('cash_discount', 0),
                    'teacher_discount': student.get('teacher_discount', 0),
                    'transfer_discount': student.get('transfer_discount', 0),
                    'personal_discount': student.get('personal_discount', 0),
                    'total_discounts': total_discounts,
                    'notes': f"تم النقل من السنة المالية 2023/2024 - الرسوم الجديدة: {new_fees}",
                    'serial_number': student.get('serial_number', ''),
                    'batch_number': student.get('batch_number', ''),
                    'file_opening_fee': student.get('file_opening_fee', 500),
                    'file_opening_date': student.get('file_opening_date', ''),
                    'transferred_file_opening': student.get('transferred_file_opening', 0),
                    'transferred_file_date': student.get('transferred_file_date', ''),
                    'total_count': student.get('total_count', 0)
                }

                # مسح جميع الأقساط الجديدة (تبدأ من صفر)
                for i in range(1, 10):
                    new_record[f'installment_{i}'] = 0
                    new_record[f'installment_{i}_date'] = None
                    new_record[f'installment_{i}_receipt'] = ''

                updated_records.append(new_record)

            # حذف البيانات القديمة
            self.db_manager.clear_all_students()

            # إدراج البيانات الجديدة
            success_count = 0
            for record in updated_records:
                try:
                    self.db_manager.insert_student_data(record)
                    success_count += 1
                except Exception as e:
                    print(f"خطأ في إدراج السجل: {e}")

            # تحديث العرض
            self.load_data()

            # رسالة النجاح
            messagebox.showinfo(
                "تم النقل بنجاح",
                f"تم نقل {success_count} طالب إلى السنة المالية الجديدة 2025\n\n"
                "تم تطبيق الرسوم الجديدة وإعادة حساب الأرصدة.\n"
                "يمكنك الآن البدء في تسجيل الأقساط الجديدة."
            )

            self.update_status(f"تم نقل {success_count} طالب للسنة الجديدة")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نقل البيانات: {str(e)}")
            self.update_status("خطأ في نقل البيانات")

    def run(self):
        """
        تشغيل التطبيق
        """
        self.root.mainloop()
