@echo off
chcp 65001 >nul
title برنامج إدارة البيانات المالية - النسخة المحسنة

echo ================================================================
echo 🚀 برنامج إدارة البيانات المالية للطلاب - النسخة المحسنة
echo ================================================================
echo.
echo ✨ الميزات المحسنة:
echo    📋 قراءة رؤوس الأعمدة الفعلية من ملفات Excel
echo    🔢 عرض الأصفار بدلاً من 'غير محدد' في الأعمدة المالية
echo    🎨 رؤوس أعمدة ملونة وتفاعلية
echo    🔍 فلاتر بحث متقدمة ديناميكية
echo    📊 إحصائيات سريعة محدثة
echo    🔄 فرز ديناميكي للأعمدة
echo    📱 شريط جانبي قابل للطي
echo    🎯 تمرير أفقي وعمودي محسن
echo    💰 معالجة متقدمة للبيانات المالية
echo    🔧 أعمدة ديناميكية تتكيف مع البيانات
echo.
echo ================================================================
echo.

echo 🔍 جاري البحث عن Python...

:: البحث عن Python في المسارات المختلفة
python --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python
    echo 🚀 جاري تشغيل البرنامج المحسن...
    echo.
    python run_main_enhanced.py
    goto :end
)

py --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python
    echo 🚀 جاري تشغيل البرنامج المحسن...
    echo.
    py run_main_enhanced.py
    goto :end
)

python3 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ تم العثور على Python
    echo 🚀 جاري تشغيل البرنامج المحسن...
    echo.
    python3 run_main_enhanced.py
    goto :end
)

echo ❌ لم يتم العثور على Python
echo.
echo 📦 يرجى تثبيت Python من:
echo    https://www.python.org/downloads/
echo.
echo 💡 تأكد من إضافة Python إلى PATH أثناء التثبيت
echo.

:end
echo.
echo ================================================================
echo 📝 ملاحظات:
echo    • إذا واجهت مشاكل، تأكد من تثبيت المكتبات المطلوبة
echo    • استخدم: pip install pandas openpyxl
echo    • للمساعدة: python run_main_enhanced.py --help
echo ================================================================
echo.
pause
