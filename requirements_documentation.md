# توثيق متطلبات برنامج نقل البيانات المالية للطلاب

## نظرة عامة
برنامج Python ديناميكي لنقل بيانات مالية لطلاب مدرسة من سنة مالية قديمة إلى سنة مالية جديدة، مع واجهة مستخدم رسومية سهلة الاستخدام.

## الأعمدة المطلوبة في البيانات
```
العدد الإجمالي، السيريال، رسم فتح ملف، تاريخ فتح الملف، فتح ملف مرحل، تاريخ فتح الملف المرحل، الدفعة، المرحلة، اسم الطالب، مديونية سابقة 23/24، مصروفات عام 2023/2024، مصروفات دراسية 2025، القسط الأول، تاريخ القسط الأول، رقم قيد القسط الأول، القسط الثاني، تاريخ القسط الثاني، رقم قيد القسط الثاني، القسط الثالث، تاريخ القسط الثالث، رقم قيد القسط الثالث، القسط الرابع، تاريخ القسط الرابع، رقم قيد القسط الرابع، القسط الخامس، تاريخ القسط الخامس، رقم قيد القسط الخامس، القسط السادس، تاريخ القسط السادس، رقم قيد القسط السادس، القسط السابع، تاريخ القسط السابع، رقم قيد القسط السابع، القسط الثامن، تاريخ القسط الثامن، رقم قيد القسط الثامن، القسط التاسع، تاريخ القسط التاسع، رقم قيد القسط التاسع، إجمالي الأقساط المدفوعة، المتبقي، خصم إخوة، خصم كاش، خصم مدرسين، خصم تحويل، خصم شخصي، إجمالي الخصومات، ملاحظات
```

## المتطلبات التقنية

### لغة البرمجة
- Python 3.x

### المكتبات المطلوبة
- `pandas` - لمعالجة ملفات Excel والبيانات
- `tkinter` - لإنشاء واجهة المستخدم الرسومية
- `sqlite3` - لإدارة قاعدة بيانات خفيفة وسهلة التعديل
- `openpyxl` - لدعم قراءة وكتابة ملفات Excel

## متطلبات واجهة المستخدم

### التصميم
- واجهة رسومية تعرض البيانات في جدول قابل للتمرير
- تشغل الواجهة 90% من مساحة الشاشة (العرض والارتفاع)
- تصميم متجاوب ونظيف

### الوظائف
- أزرار لاستيراد ملف Excel
- أزرار لتصدير البيانات إلى Excel
- أزرار لتحديث البيانات
- إمكانية تحرير البيانات مباشرة في الواجهة

## متطلبات إدارة البيانات

### استيراد البيانات
- دعم استيراد ملفات Excel تحتوي على رؤوس أعمدة مختلفة
- تخطيط (mapping) رؤوس الأعمدة القديمة إلى رؤوس أعمدة موحدة
- التعامل مع الأسماء بالعربية أو الإنجليزية أو صيغ مختلفة

### معالجة البيانات
- حساب المصروفات الدراسية الجديدة بناءً على جدول رسوم محدث
- زيادة افتراضية (مثل 10%) إذا لم يتم تحديد رسوم جديدة
- جمع البيانات القديمة (المديونية السابقة، الأقساط المدفوعة، الخصومات)
- نقل البيانات إلى السنة الجديدة مع تحديث الرصيد المتبقي
- حساب إجمالي الأقساط المدفوعة والخصومات تلقائيًا

## متطلبات قاعدة البيانات

### نوع قاعدة البيانات
- SQLite (خفيفة، لا تتطلب خادم، سهلة التعديل)

### هيكل قاعدة البيانات
#### جدول student_finance
```sql
- id (معرف فريد)
- student_name (اسم الطالب)
- academic_stage (المرحلة)
- previous_debt (مديونية سابقة)
- old_year_fees (مصروفات عام سابق)
- new_year_fees (مصروفات دراسية جديدة)
- installment_1 إلى installment_9 (الأقساط)
- total_paid (إجمالي الأقساط المدفوعة)
- remaining_balance (الرصيد المتبقي)
- sibling_discount (خصم إخوة)
- cash_discount (خصم كاش)
- teacher_discount (خصم مدرسين)
- transfer_discount (خصم تحويل)
- personal_discount (خصم شخصي)
- total_discounts (إجمالي الخصومات)
- notes (ملاحظات)
```

#### جدول fee_structure
```sql
- academic_stage (المرحلة الدراسية)
- tuition_fees (الرسوم الدراسية)
- registration_fee (رسم فتح ملف)
```

## متطلبات الديناميكية

### المرونة
- تحرير البيانات مباشرة من الواجهة أو عبر تحديث ملفات Excel
- التعامل مع اختلافات رؤوس الأعمدة بين ملفات Excel القديمة والجديدة
- دعم إضافة أقساط جديدة أو خصومات إضافية دون تغيير الكود
- تحديث جدول الرسوم بسهولة بواسطة الأقسام المالية

## متطلبات التصدير والاستيراد

### الاستيراد
- دعم ملفات Excel بصيغ مختلفة لرؤوس الأعمدة
- التحقق من صحة ملفات Excel أثناء الاستيراد
- معالجة القيم المفقودة أو غير الصحيحة

### التصدير
- تصدير البيانات إلى ملف Excel بنفس رؤوس الأعمدة الموحدة
- حفظ البيانات بتنسيق قابل للقراءة

## متطلبات معالجة الأخطاء

### التحقق من الصحة
- التحقق من صحة ملفات Excel أثناء الاستيراد
- التعامل مع القيم المفقودة أو غير الصحيحة
- تحويل القيم الفارغة إلى 0 للأرقام
- رسائل خطأ واضحة للمستخدم

## الميزات الإضافية المطلوبة

### التحسينات
- إمكانية تحرير جدول الرسوم عبر واجهة البرنامج
- دعم تقارير مالية (إجمالي المديونيات لكل مرحلة دراسية)
- إمكانية توسيع البرنامج لتصور البيانات

### سهولة الاستخدام
- واجهة نظيفة وبسيطة
- رسائل نجاح وفشل واضحة
- دليل استخدام مدمج

## خطة التنفيذ

1. إنشاء هيكل قاعدة البيانات
2. تطوير نظام تخطيط الأعمدة
3. بناء واجهة المستخدم الرسومية
4. تطوير وظائف الاستيراد والتصدير
5. إضافة معالجة الأخطاء
6. اختبار البرنامج مع بيانات تجريبية
7. توثيق الاستخدام

## ملاحظات مهمة
- البرنامج يجب أن يكون قابلاً للاستخدام من قبل أي قسم مالي
- يجب أن يدعم التحديثات المستقبلية دون تعديل الكود الأساسي
- الأمان والموثوقية في التعامل مع البيانات المالية أولوية قصوى
