@echo off
echo ================================================
echo برنامج إدارة البيانات المالية للطلاب
echo ================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.6 أو أحدث
    pause
    exit /b 1
)

echo تم العثور على Python
echo.

REM التحقق من المكتبات المطلوبة
echo جاري التحقق من المكتبات المطلوبة...
python -c "import pandas, openpyxl" >nul 2>&1
if errorlevel 1 (
    echo تثبيت المكتبات المطلوبة...
    pip install pandas openpyxl
    if errorlevel 1 (
        echo خطأ في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✓ جميع المكتبات متوفرة
echo.

REM تشغيل البرنامج
echo تشغيل البرنامج...
echo.
python main.py

echo.
echo تم إغلاق البرنامج
pause
