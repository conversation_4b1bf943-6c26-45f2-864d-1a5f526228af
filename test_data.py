#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف اختبار بسيط للبيانات المالية
"""

import pandas as pd
import os

def create_simple_test_file():
    """
    إنشاء ملف Excel بسيط للاختبار
    """
    print("📝 إنشاء ملف اختبار بسيط...")

    # بيانات اختبار بسيطة مع مشاكل حقيقية
    test_data = {
        'اسم الطالب': [
            'أحمد محمد علي',
            'فاطمة حسن',
            'محمد عبدالله',
            'نور الهدى',
            'يوسف إبراهيم'
        ],
        'المرحلة': [
            'الابتدائية',
            'الإعدادية',
            'الثانوية',
            'الابتدائية',
            'الإعدادية'
        ],
        'مديونية سابقة 23/24': [
            1000,
            None,  # خلية فارغة حقيقية
            2500,
            0,
            1500
        ],
        'مصروفات عام 2023/2024': [
            15000,
            18000,
            None,  # خلية فارغة حقيقية
            15000,
            18000
        ],
        'القسط الأول': [
            2000,
            3000,
            2500,
            None,  # خلية فارغة حقيقية
            3000
        ],
        'القسط الثاني': [
            None,  # خلية فارغة حقيقية
            3000,
            2500,
            2000,
            0
        ],
        'خصم إخوة': [
            500,
            0,
            None,  # خلية فارغة حقيقية
            500,
            1000
        ],
        'ملاحظات': [
            'طالب متفوق',
            None,  # خلية فارغة حقيقية
            'يحتاج متابعة',
            'منتظم في الدفع',
            None  # خلية فارغة حقيقية
        ]
    }

    # إنشاء DataFrame
    df = pd.DataFrame(test_data)

    try:
        # حفظ الملف
        filename = 'test_financial_data.xlsx'
        df.to_excel(filename, index=False, engine='openpyxl')

        print(f"✅ تم إنشاء ملف الاختبار: {filename}")
        print(f"📊 البيانات: {len(df)} صف و {len(df.columns)} عمود")

        # عرض البيانات
        print(f"\n📋 محتوى الملف:")
        print(df.to_string())

        return filename
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return None

def create_complex_test_file():
    """
    إنشاء ملف اختبار أكثر تعقيداً
    """
    print("\n📝 إنشاء ملف اختبار معقد...")

    # بيانات أكثر تعقيداً مع مشاكل مختلفة
    complex_data = {
        'اسم الطالب': [
            'أحمد محمد علي السيد',
            'فاطمة حسن محمود',
            'محمد عبدالله أحمد',
            '',  # اسم فارغ
            'نور الهدى محمد',
            'يوسف إبراهيم حسن',
            'مريم عبدالرحمن',
            'عبدالله محمد',
            'سارة أحمد علي',
            'حسام الدين محمد'
        ],
        'المرحلة': [
            'الابتدائية',
            'الإعدادية',
            'الثانوية',
            'الابتدائية',
            '',  # مرحلة فارغة
            'الإعدادية',
            'الثانوية',
            'الابتدائية',
            'الإعدادية',
            'الثانوية'
        ],
        'مديونية سابقة 23/24': [
            1000,
            '',  # فارغة
            2500.50,  # رقم عشري
            0,
            1500,
            '2,000',  # رقم بفاصلة
            3000,
            '',  # فارغة
            1200.75,
            0
        ],
        'مصروفات عام 2023/2024': [
            15000,
            '18,000',  # رقم بفاصلة
            '',  # فارغة
            15000,
            18000,
            20000,
            '22,500',  # رقم بفاصلة
            15000,
            '',  # فارغة
            25000
        ],
        'مصروفات دراسية 2025': [
            18000,
            20000,
            25000,
            '',  # فارغة
            20000,
            22000,
            28000,
            18000,
            20000,
            30000
        ],
        'القسط الأول': [
            2000,
            3000,
            '2,500',  # رقم بفاصلة
            '',  # فارغة
            3000,
            2500,
            4000,
            2000,
            '',  # فارغة
            5000
        ],
        'القسط الثاني': [
            '',  # فارغة
            3000,
            2500,
            2000,
            0,
            '2,500',  # رقم بفاصلة
            4000,
            '',  # فارغة
            3000,
            5000
        ],
        'القسط الثالث': [
            0,
            '',  # فارغة
            0,
            0,
            3000,
            0,
            '',  # فارغة
            0,
            3000,
            0
        ],
        'خصم إخوة': [
            500,
            0,
            '',  # فارغة
            500,
            1000,
            750,
            0,
            '',  # فارغة
            500,
            1200
        ],
        'خصم كاش': [
            0,
            '',  # فارغة
            200,
            0,
            0,
            300,
            '',  # فارغة
            0,
            150,
            0
        ],
        'ملاحظات': [
            'طالب متفوق',
            '',  # فارغة
            'يحتاج متابعة مالية',
            'اسم غير مكتمل',
            'مرحلة غير محددة',
            'منتظم في الدفع',
            'طالب جديد',
            '',  # فارغة
            'خصم خاص',
            'متفوق أكاديمياً'
        ]
    }

    # إنشاء DataFrame
    df = pd.DataFrame(complex_data)

    # حفظ الملف
    filename = 'test_complex_financial_data.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')

    print(f"✅ تم إنشاء ملف الاختبار المعقد: {filename}")
    print(f"📊 البيانات: {len(df)} صف و {len(df.columns)} عمود")

    # إحصائيات المشاكل
    empty_cells = df.isnull().sum().sum()
    total_cells = len(df) * len(df.columns)

    print(f"⚠️ إحصائيات المشاكل:")
    print(f"   📊 إجمالي الخلايا: {total_cells}")
    print(f"   🔴 خلايا فارغة: {empty_cells}")
    print(f"   💰 أرقام بفواصل: موجودة")
    print(f"   📝 بيانات ناقصة: موجودة")

    return filename

def test_file_reading():
    """
    اختبار قراءة الملفات
    """
    print("\n🧪 اختبار قراءة الملفات...")

    files_to_test = ['test_financial_data.xlsx', 'test_complex_financial_data.xlsx']

    for filename in files_to_test:
        if os.path.exists(filename):
            print(f"\n📖 اختبار قراءة: {filename}")

            try:
                # قراءة عادية
                df = pd.read_excel(filename, engine='openpyxl')
                print(f"✅ قراءة عادية: {len(df)} صف")

                # فحص القيم الفارغة
                empty_count = df.isnull().sum().sum()
                print(f"🔍 خلايا فارغة: {empty_count}")

                # فحص الأعمدة المالية
                financial_cols = [col for col in df.columns if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية'])]
                print(f"💰 أعمدة مالية: {len(financial_cols)}")

                # عرض عينة
                print(f"📋 عينة من البيانات:")
                print(df.head(3).to_string())

            except Exception as e:
                print(f"❌ خطأ في قراءة {filename}: {e}")

def main():
    """
    الدالة الرئيسية
    """
    print("🧪 إنشاء ملفات اختبار للبيانات المالية")
    print("=" * 50)

    # إنشاء الملفات
    simple_file = create_simple_test_file()
    complex_file = create_complex_test_file()

    # اختبار القراءة
    test_file_reading()

    print(f"\n🎯 ملفات الاختبار جاهزة:")
    print(f"   📄 {simple_file} - ملف بسيط")
    print(f"   📄 {complex_file} - ملف معقد")
    print(f"\n💡 يمكنك الآن اختبار البرنامج بهذه الملفات!")

if __name__ == "__main__":
    main()
