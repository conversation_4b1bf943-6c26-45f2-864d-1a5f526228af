# برنامج إدارة البيانات المالية للطلاب

برنامج Python ديناميكي لنقل بيانات مالية لطلاب مدرسة من سنة مالية قديمة إلى سنة مالية جديدة، مع واجهة مستخدم رسومية سهلة الاستخدام.

## الميزات الرئيسية

### 🔄 معالجة البيانات الديناميكية
- استيراد ملفات Excel بأشكال مختلفة لرؤوس الأعمدة
- تخطيط تلقائي للأعمدة (عربي/إنجليزي/صيغ مختلفة)
- تنظيف وتصحيح البيانات تلقائياً
- حساب المصروفات والأقساط والخصومات

### 💰 إدارة البيانات المالية
- نقل المديونيات من السنة السابقة
- حساب المصروفات الجديدة بناءً على هيكل الرسوم
- تتبع الأقساط المدفوعة والمتبقية
- إدارة أنواع الخصومات المختلفة

### 🖥️ واجهة مستخدم متقدمة
- واجهة رسومية تشغل 90% من الشاشة
- جدول قابل للتمرير لعرض البيانات
- أزرار سهلة للاستيراد والتصدير
- عرض الإحصائيات والتقارير

### 🗄️ قاعدة بيانات مرنة
- قاعدة بيانات SQLite خفيفة وسهلة التعديل
- إمكانية تحديث هيكل الرسوم
- نسخ احتياطية تلقائية

## التثبيت والتشغيل

### المتطلبات
- Python 3.6 أو أحدث
- نظام التشغيل: Windows/Linux/macOS

### خطوات التثبيت

1. **تحميل الملفات**
   ```bash
   # تأكد من وجود جميع الملفات في مجلد واحد
   main.py
   gui_interface.py
   database_manager.py
   data_processor.py
   excel_handler.py
   config.py
   requirements.txt
   ```

2. **تثبيت المكتبات المطلوبة**
   ```bash
   pip install -r requirements.txt
   ```
   
   أو تثبيت المكتبات يدوياً:
   ```bash
   pip install pandas openpyxl
   ```

3. **تشغيل البرنامج**
   ```bash
   python main.py
   ```

## دليل الاستخدام

### 1. تشغيل البرنامج لأول مرة
- عند التشغيل الأول، سيتم إنشاء قاعدة بيانات جديدة
- ستظهر واجهة فارغة جاهزة لاستيراد البيانات

### 2. استيراد بيانات الطلاب
1. انقر على زر "استيراد ملف Excel"
2. اختر ملف Excel يحتوي على بيانات الطلاب
3. سيقوم البرنامج بمعالجة البيانات تلقائياً
4. ستظهر رسالة تأكيد عند نجاح الاستيراد

### 3. عرض البيانات
- ستظهر البيانات في جدول قابل للتمرير
- يمكن التمرير أفقياً وعمودياً لرؤية جميع البيانات
- عداد الطلاب يظهر في أسفل الشاشة

### 4. تصدير البيانات
1. انقر على زر "تصدير إلى Excel"
2. اختر مكان حفظ الملف
3. سيتم حفظ جميع البيانات بتنسيق Excel

### 5. إنشاء قالب
- انقر على "إنشاء قالب" للحصول على ملف Excel فارغ
- يحتوي القالب على جميع الأعمدة المطلوبة
- يمكن استخدامه كمرجع لتنسيق البيانات

## هيكل البيانات المدعوم

### الأعمدة الأساسية
- **اسم الطالب**: اسم الطالب (مطلوب)
- **المرحلة**: المرحلة الدراسية (مطلوب)
- **مديونية سابقة 23/24**: المديونية من السنة السابقة
- **مصروفات دراسية 2025**: المصروفات للسنة الجديدة

### أعمدة الأقساط
- القسط الأول إلى القسط التاسع
- تاريخ كل قسط
- رقم قيد كل قسط

### أعمدة الخصومات
- خصم إخوة
- خصم كاش
- خصم مدرسين
- خصم تحويل
- خصم شخصي

### الأعمدة المحسوبة تلقائياً
- إجمالي الأقساط المدفوعة
- إجمالي الخصومات
- المتبقي

## المراحل الدراسية المدعومة

- رياض الأطفال
- الابتدائية
- الإعدادية
- الثانوية

## الرسوم الافتراضية

| المرحلة | الرسوم الدراسية | رسم فتح الملف |
|---------|----------------|---------------|
| رياض الأطفال | 15,000 جنيه | 500 جنيه |
| الابتدائية | 18,000 جنيه | 500 جنيه |
| الإعدادية | 20,000 جنيه | 500 جنيه |
| الثانوية | 25,000 جنيه | 500 جنيه |

## معالجة الأخطاء

### الأخطاء الشائعة وحلولها

1. **"المكتبات مفقودة"**
   - تأكد من تثبيت pandas و openpyxl
   - استخدم: `pip install -r requirements.txt`

2. **"خطأ في قراءة ملف Excel"**
   - تأكد من أن الملف ليس مفتوحاً في برنامج آخر
   - تحقق من صيغة الملف (.xlsx أو .xls)

3. **"لا يمكن الكتابة في الملف"**
   - تأكد من أن ملف التصدير ليس مفتوحاً
   - تحقق من صلاحيات الكتابة في المجلد

4. **"بيانات مفقودة أو غير صحيحة"**
   - تحقق من وجود أعمدة "اسم الطالب" و "المرحلة"
   - تأكد من أن البيانات الرقمية صحيحة

## الملفات والمجلدات

```
move-finance/
├── main.py                     # الملف الرئيسي
├── gui_interface.py            # واجهة المستخدم
├── database_manager.py         # إدارة قاعدة البيانات
├── data_processor.py           # معالجة البيانات
├── excel_handler.py            # التعامل مع Excel
├── config.py                   # الإعدادات
├── requirements.txt            # المكتبات المطلوبة
├── requirements_documentation.md # توثيق المتطلبات
├── README.md                   # دليل الاستخدام
└── school_finance.db           # قاعدة البيانات (تُنشأ تلقائياً)
```

## الدعم والمساعدة

### للحصول على المساعدة
- راجع ملف `requirements_documentation.md` للتفاصيل التقنية
- تحقق من رسائل الخطأ في واجهة البرنامج
- تأكد من صحة تنسيق ملفات Excel

### التطوير المستقبلي
- إضافة ميزات تحرير البيانات مباشرة
- تقارير مالية متقدمة
- دعم قواعد بيانات أكبر
- واجهة ويب للاستخدام عبر الشبكة

## الترخيص

هذا البرنامج مطور لأغراض تعليمية وإدارية. يمكن استخدامه وتعديله حسب الحاجة.

---

**ملاحظة**: تأكد من عمل نسخة احتياطية من بياناتك قبل استخدام البرنامج لأول مرة.
