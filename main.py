#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة البيانات المالية للطلاب
الملف الرئيسي لتشغيل البرنامج

المطور: مساعد الذكي الاصطناعي
التاريخ: 2024
الإصدار: 1.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui_interface import FinanceGUI
    from database_manager import DatabaseManager
    from config import DATABASE_NAME
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة في نفس المجلد")
    sys.exit(1)

def check_dependencies():
    """
    التحقق من وجود المكتبات المطلوبة
    """
    required_packages = ['pandas', 'openpyxl']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        error_msg = f"""
المكتبات التالية مفقودة:
{', '.join(missing_packages)}

لتثبيت المكتبات المطلوبة، قم بتشغيل الأمر التالي:
pip install {' '.join(missing_packages)}

أو استخدم ملف requirements.txt:
pip install -r requirements.txt
        """
        
        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        messagebox.showerror("مكتبات مفقودة", error_msg)
        root.destroy()
        
        print(error_msg)
        return False
    
    return True

def initialize_database():
    """
    تهيئة قاعدة البيانات
    """
    try:
        db_manager = DatabaseManager()
        print(f"تم إنشاء قاعدة البيانات: {DATABASE_NAME}")
        return True
    except Exception as e:
        error_msg = f"خطأ في تهيئة قاعدة البيانات: {str(e)}"
        print(error_msg)
        
        # إنشاء نافذة خطأ
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
        root.destroy()
        
        return False

def main():
    """
    الدالة الرئيسية لتشغيل البرنامج
    """
    print("=" * 50)
    print("برنامج إدارة البيانات المالية للطلاب")
    print("=" * 50)
    
    # التحقق من المكتبات المطلوبة
    print("جاري التحقق من المكتبات المطلوبة...")
    if not check_dependencies():
        return
    
    print("✓ جميع المكتبات متوفرة")
    
    # تهيئة قاعدة البيانات
    print("جاري تهيئة قاعدة البيانات...")
    if not initialize_database():
        return
    
    print("✓ تم تهيئة قاعدة البيانات بنجاح")
    
    # تشغيل واجهة المستخدم
    print("جاري تشغيل واجهة المستخدم...")
    
    try:
        app = FinanceGUI()
        print("✓ تم تشغيل البرنامج بنجاح")
        print("=" * 50)
        app.run()
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل البرنامج: {str(e)}"
        print(f"✗ {error_msg}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        
        # إنشاء نافذة خطأ
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
            root.destroy()
        except:
            pass

def show_help():
    """
    عرض معلومات المساعدة
    """
    help_text = """
برنامج إدارة البيانات المالية للطلاب
=====================================

الاستخدام:
    python main.py              - تشغيل البرنامج
    python main.py --help       - عرض هذه المساعدة

الميزات:
    • استيراد بيانات الطلاب من ملفات Excel
    • معالجة وتنظيف البيانات تلقائياً
    • حساب المصروفات والأقساط والخصومات
    • تصدير البيانات إلى ملفات Excel
    • إدارة هيكل الرسوم الدراسية
    • عرض الإحصائيات والتقارير

المتطلبات:
    • Python 3.6 أو أحدث
    • pandas
    • openpyxl
    • tkinter (مدمج مع Python)

للحصول على المساعدة:
    راجع ملف requirements_documentation.md
    """
    print(help_text)

if __name__ == "__main__":
    # التحقق من معاملات سطر الأوامر
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['--version', '-v']:
            print("برنامج إدارة البيانات المالية للطلاب - الإصدار 1.0")
            sys.exit(0)
        else:
            print(f"معامل غير معروف: {sys.argv[1]}")
            print("استخدم --help للحصول على المساعدة")
            sys.exit(1)
    
    # تشغيل البرنامج
    main()
