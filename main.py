#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج إدارة البيانات المالية للطلاب
الملف الرئيسي لتشغيل البرنامج

المطور: مساعد الذكي الاصطناعي
التاريخ: 2024
الإصدار: 1.0
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import tkinter as tk
    from tkinter import ttk, messagebox, filedialog
    import pandas as pd
    from database_manager import DatabaseManager
    from excel_handler import ExcelHandler
    from data_processor import DataProcessor
    from config import DATABASE_NAME
except ImportError as e:
    print(f"خطأ في استيراد المكتبات: {e}")
    print("تأكد من وجود جميع الملفات المطلوبة في نفس المجلد")
    sys.exit(1)

def check_dependencies():
    """
    التحقق من وجود المكتبات المطلوبة
    """
    required_packages = ['pandas', 'openpyxl']
    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        error_msg = f"""
المكتبات التالية مفقودة:
{', '.join(missing_packages)}

لتثبيت المكتبات المطلوبة، قم بتشغيل الأمر التالي:
pip install {' '.join(missing_packages)}

أو استخدم ملف requirements.txt:
pip install -r requirements.txt
        """

        # إنشاء نافذة خطأ بسيطة
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        messagebox.showerror("مكتبات مفقودة", error_msg)
        root.destroy()

        print(error_msg)
        return False

    return True

def initialize_database():
    """
    تهيئة قاعدة البيانات
    """
    try:
        db_manager = DatabaseManager()
        print(f"تم إنشاء قاعدة البيانات: {DATABASE_NAME}")
        return True
    except Exception as e:
        error_msg = f"خطأ في تهيئة قاعدة البيانات: {str(e)}"
        print(error_msg)

        # إنشاء نافذة خطأ
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في قاعدة البيانات", error_msg)
        root.destroy()

        return False

def main():
    """
    الدالة الرئيسية لتشغيل البرنامج
    """
    print("=" * 50)
    print("برنامج إدارة البيانات المالية للطلاب")
    print("=" * 50)

    # التحقق من المكتبات المطلوبة
    print("جاري التحقق من المكتبات المطلوبة...")
    if not check_dependencies():
        return

    print("✓ جميع المكتبات متوفرة")

    # تهيئة قاعدة البيانات
    print("جاري تهيئة قاعدة البيانات...")
    if not initialize_database():
        return

    print("✓ تم تهيئة قاعدة البيانات بنجاح")

    # تشغيل واجهة المستخدم
    print("جاري تشغيل واجهة المستخدم...")

    try:
        app = SimpleFinanceApp()
        print("✓ تم تشغيل البرنامج بنجاح")
        print("=" * 50)
        app.run()

    except Exception as e:
        error_msg = f"خطأ في تشغيل البرنامج: {str(e)}"
        print(f"✗ {error_msg}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()

        # إنشاء نافذة خطأ
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
            root.destroy()
        except:
            pass

def show_help():
    """
    عرض معلومات المساعدة
    """
    help_text = """
برنامج إدارة البيانات المالية للطلاب
=====================================

الاستخدام:
    python main.py              - تشغيل البرنامج
    python main.py --help       - عرض هذه المساعدة

الميزات:
    • استيراد بيانات الطلاب من ملفات Excel
    • معالجة وتنظيف البيانات تلقائياً
    • حساب المصروفات والأقساط والخصومات
    • تصدير البيانات إلى ملفات Excel
    • إدارة هيكل الرسوم الدراسية
    • عرض الإحصائيات والتقارير

المتطلبات:
    • Python 3.6 أو أحدث
    • pandas
    • openpyxl
    • tkinter (مدمج مع Python)

للحصول على المساعدة:
    راجع ملف requirements_documentation.md
    """
    print(help_text)

class SimpleFinanceApp:
    """واجهة مستخدم بسيطة لبرنامج إدارة البيانات المالية"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("برنامج إدارة البيانات المالية للطلاب")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # تهيئة المكونات
        self.db_manager = DatabaseManager()
        self.excel_handler = ExcelHandler()
        self.data_processor = DataProcessor(self.db_manager)

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار الأزرار
        button_frame = tk.Frame(self.root, bg='#f0f0f0')
        button_frame.pack(pady=10)

        # أزرار التحكم
        tk.Button(button_frame, text="استيراد ملف Excel", command=self.import_excel,
                 bg='#4CAF50', fg='white', font=('Arial', 12), padx=20).pack(side=tk.LEFT, padx=5)

        tk.Button(button_frame, text="تصدير إلى Excel", command=self.export_excel,
                 bg='#2196F3', fg='white', font=('Arial', 12), padx=20).pack(side=tk.LEFT, padx=5)

        tk.Button(button_frame, text="تحديث البيانات", command=self.load_data,
                 bg='#FF9800', fg='white', font=('Arial', 12), padx=20).pack(side=tk.LEFT, padx=5)

        # جدول البيانات
        self.tree_frame = tk.Frame(self.root)
        self.tree_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(self.tree_frame, orient=tk.VERTICAL)
        scrollbar_x = ttk.Scrollbar(self.tree_frame, orient=tk.HORIZONTAL)

        # جدول البيانات
        self.tree = ttk.Treeview(self.tree_frame,
                                yscrollcommand=scrollbar_y.set,
                                xscrollcommand=scrollbar_x.set)

        scrollbar_y.config(command=self.tree.yview)
        scrollbar_x.config(command=self.tree.xview)

        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        self.tree.pack(fill=tk.BOTH, expand=True)

        # شريط الحالة
        self.status_bar = tk.Label(self.root, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def load_data(self):
        """تحميل البيانات من قاعدة البيانات"""
        try:
            data = self.db_manager.get_all_students()

            # مسح البيانات السابقة
            for item in self.tree.get_children():
                self.tree.delete(item)

            if data.empty:
                self.status_bar.config(text="لا توجد بيانات")
                return

            # إعداد الأعمدة
            columns = ['اسم الطالب', 'المرحلة', 'القسط الأول', 'القسط الثاني', 'المتبقي']
            self.tree['columns'] = columns
            self.tree['show'] = 'headings'

            # إعداد رؤوس الأعمدة
            for col in columns:
                self.tree.heading(col, text=col)
                self.tree.column(col, width=150, anchor=tk.CENTER)

            # إضافة البيانات
            for _, row in data.iterrows():
                values = [
                    row.get('student_name', 'غير محدد'),
                    row.get('academic_stage', 'غير محدد'),
                    row.get('installment_1', 0),
                    row.get('installment_2', 0),
                    row.get('remaining_balance', 0)
                ]
                self.tree.insert('', tk.END, values=values)

            self.status_bar.config(text=f"تم تحميل {len(data)} سجل")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")

    def import_excel(self):
        """استيراد ملف Excel"""
        try:
            file_path = filedialog.askopenfilename(
                title="اختر ملف Excel",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )

            if not file_path:
                return

            self.status_bar.config(text="جاري استيراد البيانات...")
            self.root.update()

            # قراءة الملف
            df, warnings = self.excel_handler.import_excel_file(file_path)

            # معالجة البيانات
            processed_df, errors = self.data_processor.process_excel_data(df)

            # حفظ في قاعدة البيانات
            records = self.data_processor.prepare_for_database(processed_df)

            # مسح البيانات القديمة
            self.db_manager.clear_all_students()

            # إدراج البيانات الجديدة
            for record in records:
                self.db_manager.insert_student_data(record)

            # تحديث العرض
            self.load_data()

            messagebox.showinfo("نجح", f"تم استيراد {len(records)} سجل بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في استيراد الملف: {str(e)}")
            self.status_bar.config(text="خطأ في الاستيراد")

    def export_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            data = self.db_manager.get_all_students()

            if data.empty:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف Excel",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )

            if not file_path:
                return

            self.status_bar.config(text="جاري تصدير البيانات...")
            self.root.update()

            # تصدير البيانات
            self.excel_handler.export_to_excel(data, file_path)

            messagebox.showinfo("نجح", f"تم تصدير البيانات إلى: {file_path}")
            self.status_bar.config(text="تم التصدير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف: {str(e)}")
            self.status_bar.config(text="خطأ في التصدير")

    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

if __name__ == "__main__":
    # التحقق من معاملات سطر الأوامر
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h', 'help']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] in ['--version', '-v']:
            print("برنامج إدارة البيانات المالية للطلاب - الإصدار 1.0")
            sys.exit(0)
        else:
            print(f"معامل غير معروف: {sys.argv[1]}")
            print("استخدم --help للحصول على المساعدة")
            sys.exit(1)

    # تشغيل البرنامج
    main()
