# -*- coding: utf-8 -*-
"""
معالجة البيانات وتخطيط الأعمدة
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from config import (
    COLUMN_MAPPING, STANDARD_COLUMNS, INSTALLMENT_COLUMNS,
    DISCOUNT_COLUMNS, NUMERIC_COLUMNS, DATE_COLUMNS, DEFAULT_FEE_INCREASE
)
from database_manager import DatabaseManager

class DataProcessor:
    def __init__(self, db_manager: DatabaseManager):
        """
        تهيئة معالج البيانات
        """
        self.db_manager = db_manager
        self.reverse_mapping = self._create_reverse_mapping()

    def _create_reverse_mapping(self) -> Dict[str, str]:
        """
        إنشاء تخطيط عكسي للأعمدة
        """
        reverse_map = {}
        for standard_col, alternatives in COLUMN_MAPPING.items():
            for alt in alternatives:
                reverse_map[alt.lower().strip()] = standard_col
        return reverse_map

    def map_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        تخطيط أعمدة DataFrame إلى الأعمدة الموحدة مع تحسين البحث
        """
        mapped_df = pd.DataFrame()

        print(f"🔍 بدء تخطيط الأعمدة...")
        print(f"📋 الأعمدة الموجودة في الملف: {list(df.columns)}")

        # تحويل أسماء الأعمدة إلى أحرف صغيرة للمقارنة
        df_columns_lower = {col.lower().strip(): col for col in df.columns}

        # تخطيط الأعمدة
        mapped_count = 0
        for standard_col in STANDARD_COLUMNS:
            mapped_value = None
            found_col = None

            # البحث المباشر أولاً
            if standard_col in df.columns:
                mapped_value = df[standard_col]
                found_col = standard_col
                mapped_count += 1
            else:
                # البحث في التخطيط العكسي
                for df_col_lower, original_col in df_columns_lower.items():
                    if df_col_lower in self.reverse_mapping:
                        if self.reverse_mapping[df_col_lower] == standard_col:
                            mapped_value = df[original_col]
                            found_col = original_col
                            mapped_count += 1
                            break

                # البحث بالكلمات المفتاحية
                if mapped_value is None:
                    for df_col in df.columns:
                        if self._is_similar_column(standard_col, df_col):
                            mapped_value = df[df_col]
                            found_col = df_col
                            mapped_count += 1
                            break

            # إذا لم يتم العثور على العمود، إنشاء عمود فارغ
            if mapped_value is None:
                if standard_col in NUMERIC_COLUMNS:
                    mapped_value = pd.Series([None] * len(df), dtype=object)  # استخدام None بدلاً من 0
                elif standard_col in DATE_COLUMNS:
                    mapped_value = pd.Series([None] * len(df), dtype=object)
                else:
                    mapped_value = pd.Series([None] * len(df), dtype=object)
                print(f"⚠️ لم يتم العثور على العمود: {standard_col}")
            else:
                print(f"✅ تم تخطيط '{found_col}' إلى '{standard_col}'")

            mapped_df[standard_col] = mapped_value

        print(f"📊 تم تخطيط {mapped_count} عمود من أصل {len(STANDARD_COLUMNS)}")
        return mapped_df

    def _is_similar_column(self, standard_col: str, df_col: str) -> bool:
        """
        التحقق من تشابه أسماء الأعمدة
        """
        # كلمات مفتاحية للبحث
        keywords = {
            'اسم الطالب': ['اسم', 'طالب', 'name', 'student'],
            'المرحلة': ['مرحلة', 'stage', 'level', 'grade'],
            'مديونية': ['مديونية', 'debt', 'مدين'],
            'مصروفات': ['مصروفات', 'fees', 'رسوم'],
            'قسط': ['قسط', 'installment', 'payment'],
            'خصم': ['خصم', 'discount'],
            'ملاحظات': ['ملاحظات', 'notes', 'تعليق']
        }

        standard_lower = standard_col.lower()
        df_lower = df_col.lower()

        # البحث عن الكلمات المفتاحية
        for key, words in keywords.items():
            if key in standard_lower:
                for word in words:
                    if word in df_lower:
                        return True

        return False

    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        تنظيف البيانات ومعالجة القيم المفقودة
        """
        cleaned_df = df.copy()

        # معالجة الأعمدة الرقمية
        for col in NUMERIC_COLUMNS:
            if col in cleaned_df.columns:
                # تحويل القيم إلى رقمية وتعويض القيم المفقودة بـ 0
                cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce').fillna(0)

        # معالجة الأعمدة النصية
        text_columns = [col for col in STANDARD_COLUMNS if col not in NUMERIC_COLUMNS and col not in DATE_COLUMNS]
        for col in text_columns:
            if col in cleaned_df.columns:
                cleaned_df[col] = cleaned_df[col].astype(str).fillna('')

        # معالجة أعمدة التواريخ
        for col in DATE_COLUMNS:
            if col in cleaned_df.columns:
                cleaned_df[col] = pd.to_datetime(cleaned_df[col], errors='coerce')

        return cleaned_df

    def calculate_financial_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        حساب البيانات المالية (الأقساط المدفوعة، الخصومات، المتبقي)
        """
        calculated_df = df.copy()

        # حساب إجمالي الأقساط المدفوعة
        installment_sum = 0
        for col in INSTALLMENT_COLUMNS:
            if col in calculated_df.columns:
                installment_sum += calculated_df[col].fillna(0)
        calculated_df['إجمالي الأقساط المدفوعة'] = installment_sum

        # حساب إجمالي الخصومات
        discount_sum = 0
        for col in DISCOUNT_COLUMNS:
            if col in calculated_df.columns:
                discount_sum += calculated_df[col].fillna(0)
        calculated_df['إجمالي الخصومات'] = discount_sum

        # حساب المتبقي
        total_fees = calculated_df['مصروفات دراسية 2025'].fillna(0)
        previous_debt = calculated_df['مديونية سابقة 23/24'].fillna(0)
        total_paid = calculated_df['إجمالي الأقساط المدفوعة'].fillna(0)
        total_discounts = calculated_df['إجمالي الخصومات'].fillna(0)

        calculated_df['المتبقي'] = (total_fees + previous_debt) - (total_paid + total_discounts)

        return calculated_df

    def update_new_year_fees(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        تحديث مصروفات السنة الجديدة بناءً على هيكل الرسوم
        """
        updated_df = df.copy()
        fee_structure = self.db_manager.get_fee_structure()

        if not fee_structure.empty:
            # إنشاء قاموس للرسوم حسب المرحلة
            fee_dict = dict(zip(fee_structure['academic_stage'], fee_structure['tuition_fees']))

            # تحديث الرسوم لكل طالب حسب مرحلته
            for idx, row in updated_df.iterrows():
                stage = row.get('المرحلة', '')
                if stage in fee_dict:
                    updated_df.at[idx, 'مصروفات دراسية 2025'] = fee_dict[stage]
                else:
                    # إذا لم توجد رسوم محددة، استخدم زيادة افتراضية
                    old_fees = row.get('مصروفات عام 2023/2024', 0)
                    if old_fees > 0:
                        updated_df.at[idx, 'مصروفات دراسية 2025'] = old_fees * (1 + DEFAULT_FEE_INCREASE)

        return updated_df

    def validate_data(self, df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        التحقق من صحة البيانات
        """
        errors = []

        # التحقق من وجود الأعمدة الأساسية
        required_columns = ['اسم الطالب', 'المرحلة']
        for col in required_columns:
            if col not in df.columns:
                errors.append(f"العمود المطلوب '{col}' غير موجود")
            elif df[col].isna().all():
                errors.append(f"العمود '{col}' فارغ تماماً")

        # التحقق من وجود أسماء طلاب
        if 'اسم الطالب' in df.columns:
            empty_names = df[df['اسم الطالب'].isna() | (df['اسم الطالب'] == '')].index
            if len(empty_names) > 0:
                errors.append(f"يوجد {len(empty_names)} طالب بدون اسم في الصفوف: {list(empty_names + 1)}")

        # التحقق من القيم الرقمية السالبة في الأقساط
        for col in INSTALLMENT_COLUMNS:
            if col in df.columns:
                negative_values = df[df[col] < 0].index
                if len(negative_values) > 0:
                    errors.append(f"يوجد قيم سالبة في '{col}' في الصفوف: {list(negative_values + 1)}")

        # التحقق من القيم الرقمية السالبة في الخصومات
        for col in DISCOUNT_COLUMNS:
            if col in df.columns:
                negative_values = df[df[col] < 0].index
                if len(negative_values) > 0:
                    errors.append(f"يوجد قيم سالبة في '{col}' في الصفوف: {list(negative_values + 1)}")

        return len(errors) == 0, errors

    def process_excel_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, List[str]]:
        """
        معالجة شاملة لبيانات Excel
        """
        try:
            # 1. تخطيط الأعمدة
            mapped_df = self.map_columns(df)

            # 2. تنظيف البيانات
            cleaned_df = self.clean_data(mapped_df)

            # 3. تحديث رسوم السنة الجديدة
            updated_df = self.update_new_year_fees(cleaned_df)

            # 4. حساب البيانات المالية
            calculated_df = self.calculate_financial_data(updated_df)

            # 5. التحقق من صحة البيانات
            is_valid, errors = self.validate_data(calculated_df)

            if not is_valid:
                return calculated_df, errors

            return calculated_df, []

        except Exception as e:
            return pd.DataFrame(), [f"خطأ في معالجة البيانات: {str(e)}"]

    def prepare_for_database(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        تحضير البيانات للإدراج في قاعدة البيانات
        """
        records = []

        # تخطيط أسماء الأعمدة إلى أسماء قاعدة البيانات
        db_column_mapping = {
            'العدد الإجمالي': 'total_count',
            'السيريال': 'serial_number',
            'رسم فتح ملف': 'file_opening_fee',
            'تاريخ فتح الملف': 'file_opening_date',
            'فتح ملف مرحل': 'transferred_file_opening',
            'تاريخ فتح الملف المرحل': 'transferred_file_date',
            'الدفعة': 'batch_number',
            'المرحلة': 'academic_stage',
            'اسم الطالب': 'student_name',
            'مديونية سابقة 23/24': 'previous_debt_2324',
            'مصروفات عام 2023/2024': 'old_year_fees_2324',
            'مصروفات دراسية 2025': 'new_year_fees_2025',
            'إجمالي الأقساط المدفوعة': 'total_paid',
            'المتبقي': 'remaining_balance',
            'خصم إخوة': 'sibling_discount',
            'خصم كاش': 'cash_discount',
            'خصم مدرسين': 'teacher_discount',
            'خصم تحويل': 'transfer_discount',
            'خصم شخصي': 'personal_discount',
            'إجمالي الخصومات': 'total_discounts',
            'ملاحظات': 'notes'
        }

        # إضافة أعمدة الأقساط
        installment_names = ["الأول", "الثاني", "الثالث", "الرابع", "الخامس", "السادس", "السابع", "الثامن", "التاسع"]
        for i in range(1, 10):
            name = installment_names[i-1]
            db_column_mapping[f'القسط {name}'] = f'installment_{i}'
            db_column_mapping[f'تاريخ القسط {name}'] = f'installment_{i}_date'
            db_column_mapping[f'رقم قيد القسط {name}'] = f'installment_{i}_receipt'

        for _, row in df.iterrows():
            record = {}
            for excel_col, db_col in db_column_mapping.items():
                if excel_col in df.columns:
                    value = row[excel_col]
                    # معالجة القيم الفارغة والتواريخ
                    if pd.isna(value):
                        record[db_col] = None if db_col.endswith('_date') else (0 if db_col in ['total_count', 'file_opening_fee', 'transferred_file_opening', 'previous_debt_2324', 'old_year_fees_2324', 'new_year_fees_2025', 'total_paid', 'remaining_balance', 'sibling_discount', 'cash_discount', 'teacher_discount', 'transfer_discount', 'personal_discount', 'total_discounts'] + [f'installment_{i}' for i in range(1, 10)] else '')
                    else:
                        record[db_col] = value

            records.append(record)

        return records
