# -*- coding: utf-8 -*-
"""
واجهة مستخدم محسنة مع فلاتر وتمرير وألوان
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
from typing import Optional, List, Dict, Any
from database_manager import DatabaseManager
from data_processor import DataProcessor
from excel_handler import ExcelHandler
from config import STANDARD_COLUMNS

class EnhancedFinanceGUI:
    def __init__(self):
        """
        تهيئة واجهة المستخدم المحسنة
        """
        self.root = tk.Tk()
        self.root.title("برنامج إدارة البيانات المالية للطلاب - النسخة المحسنة")

        # تحديد حجم النافذة لتشغل 90% من الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        window_width = int(screen_width * 0.9)
        window_height = int(screen_height * 0.9)

        # توسيط النافذة
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.minsize(1000, 600)

        # تهيئة المكونات
        self.db_manager = DatabaseManager()
        self.data_processor = DataProcessor(self.db_manager)
        self.excel_handler = ExcelHandler()

        # متغيرات البيانات
        self.current_data = pd.DataFrame()
        self.filtered_data = pd.DataFrame()
        self.sort_reverse = {}  # لتتبع اتجاه الفرز لكل عمود

        # متغيرات الواجهة
        self.sidebar_expanded = True
        self.search_vars = {}

        # إعداد الأنماط
        self.setup_styles()

        # إعداد الواجهة
        self.setup_ui()
        self.load_data()

    def setup_styles(self):
        """
        إعداد أنماط الواجهة
        """
        style = ttk.Style()
        style.theme_use('clam')

        # نمط رؤوس الأعمدة - مع إصلاح مشكلة الهوفر
        style.configure(
            "Treeview.Heading",
            background='#4a90e2',
            foreground='white',
            font=('Arial', 12, 'bold'),  # خط أكبر
            relief='raised',
            borderwidth=1,
            focuscolor='none'  # إزالة لون التركيز
        )

        # إصلاح مشكلة الهوفر - الحفاظ على لون النص الأبيض
        style.map("Treeview.Heading",
                 background=[('active', '#3a7bc8'),  # لون أغمق عند الهوفر
                           ('pressed', '#2a6bb8')],   # لون أغمق عند الضغط
                 foreground=[('active', 'white'),     # نص أبيض عند الهوفر
                           ('pressed', 'white'),      # نص أبيض عند الضغط
                           ('!active', 'white')])     # نص أبيض في الحالة العادية

        # نمط الجدول - خط أكبر
        style.configure(
            "Treeview",
            background='white',
            foreground='black',
            rowheight=30,  # ارتفاع أكبر للصفوف
            fieldbackground='white',
            font=('Arial', 11)  # خط أكبر للبيانات
        )

        # نمط الصفوف المتناوبة
        style.map("Treeview",
                 background=[('selected', '#0078d4')],
                 foreground=[('selected', 'white')])

        # نمط الأزرار المميزة - خط أكبر
        style.configure(
            "Accent.TButton",
            background='#0078d4',
            foreground='white',
            font=('Arial', 11, 'bold')  # خط أكبر
        )

        # نمط الأزرار العادية - خط أكبر
        style.configure(
            "TButton",
            font=('Arial', 10)  # خط أكبر للأزرار العادية
        )

        # نمط التسميات - خط أكبر
        style.configure(
            "TLabel",
            font=('Arial', 10)  # خط أكبر للتسميات
        )

        # نمط حقول الإدخال - خط أكبر
        style.configure(
            "TEntry",
            font=('Arial', 11),  # خط أكبر لحقول الإدخال
            fieldbackground='white'
        )

        # نمط القوائم المنسدلة - خط أكبر
        style.configure(
            "TCombobox",
            font=('Arial', 10)  # خط أكبر للقوائم المنسدلة
        )

        # نمط إطار البحث
        style.configure(
            "Search.TFrame",
            background='#f0f0f0',
            relief='sunken',
            borderwidth=1
        )

    def setup_ui(self):
        """
        إعداد واجهة المستخدم
        """
        # الإطار الرئيسي
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # شريط الأدوات العلوي
        self.create_toolbar(main_container)

        # إطار البحث والفلاتر
        self.create_search_frame(main_container)

        # الإطار الأفقي للمحتوى والشريط الجانبي
        content_frame = ttk.Frame(main_container)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # منطقة عرض البيانات (الجدول)
        self.create_data_display(content_frame)

        # الشريط الجانبي
        self.create_sidebar(content_frame)

        # شريط الحالة السفلي
        self.create_status_bar(main_container)

    def create_toolbar(self, parent):
        """
        إنشاء شريط الأدوات المحسن
        """
        toolbar_frame = ttk.Frame(parent, style='Search.TFrame')
        toolbar_frame.pack(fill=tk.X, pady=(0, 5))

        # الصف الأول من الأزرار
        row1_frame = ttk.Frame(toolbar_frame)
        row1_frame.pack(fill=tk.X, padx=10, pady=5)

        # أزرار الملفات
        file_frame = ttk.LabelFrame(row1_frame, text="إدارة الملفات", padding=5)
        file_frame.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(file_frame, text="📁 استيراد Excel", command=self.import_excel, width=15).pack(side=tk.LEFT, padx=2)
        ttk.Button(file_frame, text="💾 تصدير Excel", command=self.export_excel, width=15).pack(side=tk.LEFT, padx=2)
        ttk.Button(file_frame, text="📋 إنشاء قالب", command=self.create_template, width=15).pack(side=tk.LEFT, padx=2)

        # أزرار البيانات
        data_frame = ttk.LabelFrame(row1_frame, text="إدارة البيانات", padding=5)
        data_frame.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(data_frame, text="💰 إدارة الرسوم", command=self.manage_fees, width=15).pack(side=tk.LEFT, padx=2)
        ttk.Button(data_frame, text="🔄 نقل للسنة الجديدة", command=self.transfer_to_new_year,
                  width=18, style='Accent.TButton').pack(side=tk.LEFT, padx=2)

        # أزرار التحليل
        analysis_frame = ttk.LabelFrame(row1_frame, text="التحليل والتقارير", padding=5)
        analysis_frame.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(analysis_frame, text="📊 الإحصائيات", command=self.show_statistics, width=15).pack(side=tk.LEFT, padx=2)
        ttk.Button(analysis_frame, text="🔍 تحليل البيانات", command=self.analyze_data, width=15).pack(side=tk.LEFT, padx=2)

        # زر التحديث على اليمين
        ttk.Button(row1_frame, text="🔄 تحديث", command=self.refresh_data, width=12).pack(side=tk.RIGHT)

    def create_search_frame(self, parent):
        """
        إنشاء إطار البحث والفلاتر المتقدم
        """
        search_frame = ttk.LabelFrame(parent, text="البحث والفلاتر", style='Search.TFrame', padding=10)
        search_frame.pack(fill=tk.X, pady=(0, 5))

        # الصف الأول - البحث العام
        general_search_frame = ttk.Frame(search_frame)
        general_search_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(general_search_frame, text="البحث العام:", font=('Arial', 12, 'bold')).pack(side=tk.LEFT, padx=(0, 5))

        self.general_search_var = tk.StringVar()
        self.general_search_var.trace('w', self.on_general_search_change)
        general_search_entry = ttk.Entry(general_search_frame, textvariable=self.general_search_var, width=30)
        general_search_entry.pack(side=tk.LEFT, padx=(0, 10))

        # اختيار العمود للبحث
        ttk.Label(general_search_frame, text="البحث في:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_column_var = tk.StringVar(value="جميع الأعمدة")
        search_column_combo = ttk.Combobox(general_search_frame, textvariable=self.search_column_var,
                                         values=["جميع الأعمدة"] + STANDARD_COLUMNS, width=20, state="readonly")
        search_column_combo.pack(side=tk.LEFT, padx=(0, 10))
        search_column_combo.bind('<<ComboboxSelected>>', self.on_search_column_change)

        # أزرار التحكم
        ttk.Button(general_search_frame, text="🗑️ مسح الفلاتر", command=self.clear_filters).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(general_search_frame, text="📋 نسخ النتائج", command=self.copy_filtered_results).pack(side=tk.LEFT, padx=(5, 0))

        # الصف الثاني - فلاتر متقدمة
        advanced_frame = ttk.Frame(search_frame)
        advanced_frame.pack(fill=tk.X)

        # فلتر المرحلة الدراسية
        ttk.Label(advanced_frame, text="المرحلة:").pack(side=tk.LEFT, padx=(0, 5))
        self.stage_filter_var = tk.StringVar(value="الكل")
        stage_combo = ttk.Combobox(advanced_frame, textvariable=self.stage_filter_var,
                                  values=["الكل", "رياض الأطفال", "الابتدائية", "الإعدادية", "الثانوية"],
                                  width=15, state="readonly")
        stage_combo.pack(side=tk.LEFT, padx=(0, 10))
        stage_combo.bind('<<ComboboxSelected>>', self.apply_filters)

        # فلتر المديونية
        ttk.Label(advanced_frame, text="المديونية:").pack(side=tk.LEFT, padx=(0, 5))
        self.debt_filter_var = tk.StringVar(value="الكل")
        debt_combo = ttk.Combobox(advanced_frame, textvariable=self.debt_filter_var,
                                 values=["الكل", "لديه مديونية", "لا توجد مديونية", "مديونية عالية (>10000)"],
                                 width=20, state="readonly")
        debt_combo.pack(side=tk.LEFT, padx=(0, 10))
        debt_combo.bind('<<ComboboxSelected>>', self.apply_filters)

        # عداد النتائج
        self.results_count_label = ttk.Label(advanced_frame, text="", font=('Arial', 11, 'italic'))
        self.results_count_label.pack(side=tk.RIGHT, padx=(10, 0))

    def create_data_display(self, parent):
        """
        إنشاء منطقة عرض البيانات المحسنة مع التمرير
        """
        # إطار الجدول الرئيسي
        table_container = ttk.Frame(parent)
        table_container.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # إطار الجدول مع التمرير
        table_frame = ttk.Frame(table_container)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # إنشاء Treeview مع جميع الأعمدة
        columns = ['رقم الصف'] + STANDARD_COLUMNS
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # تعيين رؤوس الأعمدة مع الألوان والتفاعل
        for col in columns:
            self.tree.heading(col, text=col, anchor=tk.CENTER,
                            command=lambda c=col: self.sort_by_column(c))

            # تحديد عرض العمود
            if col == 'رقم الصف':
                self.tree.column(col, width=80, minwidth=60, anchor=tk.CENTER)
            elif 'تاريخ' in col:
                self.tree.column(col, width=100, minwidth=80, anchor=tk.CENTER)
            elif any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية']):
                self.tree.column(col, width=120, minwidth=100, anchor=tk.E)
            else:
                self.tree.column(col, width=150, minwidth=100, anchor=tk.W)

        # أشرطة التمرير المحسنة
        # التمرير العمودي
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=v_scrollbar.set)

        # التمرير الأفقي
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(xscrollcommand=h_scrollbar.set)

        # ترتيب العناصر مع التمرير
        self.tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # تكوين الشبكة للتمدد
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        self.tree.bind('<Button-3>', self.show_context_menu)  # النقر بالزر الأيمن

        # تطبيق الألوان المتناوبة للصفوف
        self.tree.tag_configure('oddrow', background='#f0f0f0')
        self.tree.tag_configure('evenrow', background='white')
        self.tree.tag_configure('highlight', background='#ffffcc')

    def create_sidebar(self, parent):
        """
        إنشاء الشريط الجانبي القابل للطي
        """
        self.sidebar_frame = ttk.Frame(parent, width=250)
        self.sidebar_frame.pack(side=tk.RIGHT, fill=tk.Y)
        self.sidebar_frame.pack_propagate(False)

        # زر الطي/الفرد
        toggle_frame = ttk.Frame(self.sidebar_frame)
        toggle_frame.pack(fill=tk.X, pady=(0, 5))

        self.toggle_button = ttk.Button(toggle_frame, text="◀", command=self.toggle_sidebar, width=3)
        self.toggle_button.pack(side=tk.LEFT)

        sidebar_title = ttk.Label(toggle_frame, text="لوحة التحكم", font=('Arial', 12, 'bold'))
        sidebar_title.pack(side=tk.LEFT, padx=(10, 0))

        # محتوى الشريط الجانبي
        self.sidebar_content = ttk.Frame(self.sidebar_frame)
        self.sidebar_content.pack(fill=tk.BOTH, expand=True, padx=5)

        # قسم إعدادات العرض
        display_frame = ttk.LabelFrame(self.sidebar_content, text="إعدادات العرض", padding=5)
        display_frame.pack(fill=tk.X, pady=(0, 10))

        # خيارات إخفاء/إظهار الأعمدة
        ttk.Label(display_frame, text="إظهار الأعمدة:", font=('Arial', 11, 'bold')).pack(anchor=tk.W)

        self.column_vars = {}
        important_columns = ['اسم الطالب', 'المرحلة', 'المتبقي', 'إجمالي الأقساط المدفوعة']

        for col in important_columns:
            var = tk.BooleanVar(value=True)
            self.column_vars[col] = var
            cb = ttk.Checkbutton(display_frame, text=col, variable=var,
                               command=self.update_column_visibility)
            cb.pack(anchor=tk.W, padx=10)

        # قسم الإحصائيات السريعة
        stats_frame = ttk.LabelFrame(self.sidebar_content, text="إحصائيات سريعة", padding=5)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        self.stats_labels = {}
        stats_items = ['إجمالي الطلاب', 'إجمالي المديونيات', 'متوسط المديونية']

        for item in stats_items:
            label = ttk.Label(stats_frame, text=f"{item}: --", font=('Arial', 10))
            label.pack(anchor=tk.W)
            self.stats_labels[item] = label

        # قسم الإجراءات السريعة
        actions_frame = ttk.LabelFrame(self.sidebar_content, text="إجراءات سريعة", padding=5)
        actions_frame.pack(fill=tk.X)

        ttk.Button(actions_frame, text="تصدير المرشح", command=self.export_filtered_data, width=20).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="طباعة التقرير", command=self.print_report, width=20).pack(fill=tk.X, pady=2)
        ttk.Button(actions_frame, text="نسخ احتياطية", command=self.create_backup, width=20).pack(fill=tk.X, pady=2)

    def create_status_bar(self, parent):
        """
        إنشاء شريط الحالة المحسن
        """
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, pady=(5, 0))

        # شريط التقدم
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # تسميات الحالة
        self.status_label = ttk.Label(status_frame, text="جاهز", relief=tk.SUNKEN, anchor=tk.W)
        self.status_label.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.count_label = ttk.Label(status_frame, text="عدد الطلاب: 0", relief=tk.SUNKEN)
        self.count_label.pack(side=tk.RIGHT, padx=(5, 0))

        self.time_label = ttk.Label(status_frame, text="", relief=tk.SUNKEN)
        self.time_label.pack(side=tk.RIGHT, padx=(5, 0))

        # تحديث الوقت
        self.update_time()

    def update_time(self):
        """
        تحديث عرض الوقت
        """
        import datetime
        current_time = datetime.datetime.now().strftime("%H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)

    # الدوال المساعدة للواجهة
    def toggle_sidebar(self):
        """
        طي/فرد الشريط الجانبي
        """
        if self.sidebar_expanded:
            self.sidebar_frame.configure(width=50)
            self.toggle_button.configure(text="▶")
            self.sidebar_content.pack_forget()
        else:
            self.sidebar_frame.configure(width=250)
            self.toggle_button.configure(text="◀")
            self.sidebar_content.pack(fill=tk.BOTH, expand=True, padx=5)

        self.sidebar_expanded = not self.sidebar_expanded

    def sort_by_column(self, col):
        """
        فرز البيانات حسب العمود
        """
        if self.filtered_data.empty:
            return

        # تحديد اتجاه الفرز
        reverse = self.sort_reverse.get(col, False)
        self.sort_reverse[col] = not reverse

        try:
            # فرز البيانات
            if col == 'رقم الصف':
                sorted_data = self.filtered_data.sort_index(ascending=not reverse)
            else:
                # تحديد نوع البيانات للفرز
                if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية']):
                    # فرز رقمي
                    sorted_data = self.filtered_data.sort_values(
                        by=col, ascending=not reverse,
                        key=lambda x: pd.to_numeric(x, errors='coerce').fillna(0)
                    )
                else:
                    # فرز نصي
                    sorted_data = self.filtered_data.sort_values(by=col, ascending=not reverse)

            self.filtered_data = sorted_data
            self.populate_tree()

            # تحديث رأس العمود لإظهار اتجاه الفرز
            arrow = " ↓" if reverse else " ↑"
            self.tree.heading(col, text=col + arrow)

            # إزالة الأسهم من الأعمدة الأخرى
            for other_col in self.tree['columns']:
                if other_col != col:
                    clean_text = other_col.replace(" ↑", "").replace(" ↓", "")
                    self.tree.heading(other_col, text=clean_text)

        except Exception as e:
            messagebox.showerror("خطأ في الفرز", f"لا يمكن فرز العمود '{col}': {str(e)}")

    def on_general_search_change(self, *args):
        """
        معالج تغيير البحث العام
        """
        self.root.after_idle(self.apply_filters)

    def on_search_column_change(self, event=None):
        """
        معالج تغيير عمود البحث
        """
        self.apply_filters()

    def apply_filters(self, event=None):
        """
        تطبيق جميع الفلاتر على البيانات
        """
        if self.current_data.empty:
            return

        filtered_data = self.current_data.copy()

        # فلتر البحث العام
        search_text = self.general_search_var.get().strip()
        search_column = self.search_column_var.get()

        if search_text:
            if search_column == "جميع الأعمدة":
                # البحث في جميع الأعمدة
                mask = filtered_data.astype(str).apply(
                    lambda x: x.str.contains(search_text, case=False, na=False)
                ).any(axis=1)
                filtered_data = filtered_data[mask]
            else:
                # البحث في عمود محدد
                if search_column in filtered_data.columns:
                    mask = filtered_data[search_column].astype(str).str.contains(
                        search_text, case=False, na=False
                    )
                    filtered_data = filtered_data[mask]

        # فلتر المرحلة الدراسية
        stage_filter = self.stage_filter_var.get()
        if stage_filter != "الكل" and 'المرحلة' in filtered_data.columns:
            filtered_data = filtered_data[filtered_data['المرحلة'] == stage_filter]

        # فلتر المديونية
        debt_filter = self.debt_filter_var.get()
        if debt_filter != "الكل" and 'المتبقي' in filtered_data.columns:
            debt_col = pd.to_numeric(filtered_data['المتبقي'], errors='coerce').fillna(0)

            if debt_filter == "لديه مديونية":
                filtered_data = filtered_data[debt_col > 0]
            elif debt_filter == "لا توجد مديونية":
                filtered_data = filtered_data[debt_col <= 0]
            elif debt_filter == "مديونية عالية (>10000)":
                filtered_data = filtered_data[debt_col > 10000]

        self.filtered_data = filtered_data
        self.populate_tree()
        self.update_results_count()
        self.update_quick_stats()

    def clear_filters(self):
        """
        مسح جميع الفلاتر
        """
        self.general_search_var.set("")
        self.search_column_var.set("جميع الأعمدة")
        self.stage_filter_var.set("الكل")
        self.debt_filter_var.set("الكل")
        self.filtered_data = self.current_data.copy()
        self.populate_tree()
        self.update_results_count()
        self.update_quick_stats()

    def update_results_count(self):
        """
        تحديث عداد النتائج
        """
        total = len(self.current_data)
        filtered = len(self.filtered_data)

        if filtered == total:
            self.results_count_label.config(text=f"عرض جميع النتائج ({total})")
        else:
            self.results_count_label.config(text=f"عرض {filtered} من {total} نتيجة")

    def update_quick_stats(self):
        """
        تحديث الإحصائيات السريعة في الشريط الجانبي
        """
        if self.filtered_data.empty:
            for label in self.stats_labels.values():
                label.config(text=label.cget("text").split(":")[0] + ": --")
            return

        # إجمالي الطلاب
        total_students = len(self.filtered_data)
        self.stats_labels['إجمالي الطلاب'].config(text=f"إجمالي الطلاب: {total_students}")

        # إجمالي المديونيات
        if 'المتبقي' in self.filtered_data.columns:
            debt_col = pd.to_numeric(self.filtered_data['المتبقي'], errors='coerce').fillna(0)
            total_debt = debt_col.sum()
            self.stats_labels['إجمالي المديونيات'].config(text=f"إجمالي المديونيات: {total_debt:,.0f} ج.م")

            # متوسط المديونية
            avg_debt = debt_col.mean()
            self.stats_labels['متوسط المديونية'].config(text=f"متوسط المديونية: {avg_debt:,.0f} ج.م")

    def update_column_visibility(self):
        """
        تحديث رؤية الأعمدة
        """
        # هذه الميزة تحتاج تطوير إضافي
        pass

    def populate_tree(self):
        """
        ملء الجدول بالبيانات المفلترة مع الألوان المتناوبة
        """
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        if self.filtered_data.empty:
            self.update_count(0)
            return

        # إضافة البيانات الجديدة مع الألوان المتناوبة
        columns = list(self.tree['columns'])

        for idx, (_, row) in enumerate(self.filtered_data.iterrows()):
            values = []

            for col in columns:
                if col == 'رقم الصف':
                    values.append(idx + 1)
                elif col in self.filtered_data.columns:
                    value = row[col]
                    if pd.isna(value):
                        values.append('غير محدد')
                    elif isinstance(value, (int, float)) and col != 'رقم الصف':
                        # تنسيق الأرقام المالية
                        if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية']):
                            values.append(f"{value:,.0f}")
                        else:
                            values.append(str(value))
                    else:
                        values.append(str(value))
                else:
                    values.append('')

            # تحديد لون الصف
            tag = 'evenrow' if idx % 2 == 0 else 'oddrow'

            # إضافة تمييز للمديونيات العالية
            if 'المتبقي' in self.filtered_data.columns:
                debt_value = pd.to_numeric(row.get('المتبقي', 0), errors='coerce')
                if debt_value > 10000:
                    tag = 'highlight'

            item_id = self.tree.insert('', tk.END, values=values, tags=(tag,))

        self.update_count(len(self.filtered_data))

    def load_data(self):
        """
        تحميل البيانات من قاعدة البيانات
        """
        try:
            self.update_status("جاري تحميل البيانات...")
            self.progress_bar.start()

            self.current_data = self.db_manager.get_all_students()
            self.filtered_data = self.current_data.copy()

            self.populate_tree()
            self.update_results_count()
            self.update_quick_stats()

            self.progress_bar.stop()
            self.update_status("تم تحميل البيانات بنجاح")

        except Exception as e:
            self.progress_bar.stop()
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {str(e)}")
            self.update_status("خطأ في تحميل البيانات")

    def update_status(self, message: str):
        """
        تحديث شريط الحالة
        """
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def update_count(self, count: int):
        """
        تحديث عداد الطلاب
        """
        self.count_label.config(text=f"عدد الطلاب: {count}")

    # دوال الأحداث
    def on_item_double_click(self, event):
        """
        معالج النقر المزدوج على عنصر في الجدول
        """
        selection = self.tree.selection()
        if selection:
            item = self.tree.item(selection[0])
            values = item['values']
            if values:
                student_name = values[self.tree['columns'].index('اسم الطالب') + 1] if 'اسم الطالب' in self.tree['columns'] else "غير محدد"
                messagebox.showinfo("تفاصيل الطالب", f"تم اختيار الطالب: {student_name}")

    def show_context_menu(self, event):
        """
        عرض قائمة السياق عند النقر بالزر الأيمن
        """
        # إنشاء قائمة سياق
        context_menu = tk.Menu(self.root, tearoff=0)
        context_menu.add_command(label="نسخ", command=self.copy_selected)
        context_menu.add_command(label="تحرير", command=self.edit_selected)
        context_menu.add_separator()
        context_menu.add_command(label="حذف", command=self.delete_selected)

        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    # دوال الإجراءات المكتملة
    def import_excel(self):
        """استيراد ملف Excel مع المعالجة المحسنة"""
        try:
            self.update_status("جاري استيراد ملف Excel...")
            self.progress_bar.start()

            # استيراد الملف مع المعالجة المحسنة
            df, warnings = self.excel_handler.import_excel_file()

            if df.empty:
                self.progress_bar.stop()
                messagebox.showwarning("تحذير", "الملف فارغ أو لا يحتوي على بيانات صالحة")
                self.update_status("الملف فارغ")
                return

            print(f"📊 تم قراءة {len(df)} صف و {len(df.columns)} عمود")
            print(f"📋 أسماء الأعمدة: {list(df.columns)}")

            # عرض تحذيرات إن وجدت
            if warnings:
                warning_msg = "تم استيراد الملف مع التحذيرات التالية:\n\n" + "\n".join(warnings[:10])
                if len(warnings) > 10:
                    warning_msg += f"\n... و {len(warnings) - 10} تحذير إضافي"

                if not messagebox.askyesno("تحذيرات في البيانات",
                                         f"{warning_msg}\n\nهل تريد المتابعة؟"):
                    self.progress_bar.stop()
                    self.update_status("تم إلغاء الاستيراد")
                    return

            # معالجة البيانات
            processed_df, processing_errors = self.data_processor.process_excel_data(df)

            if processing_errors:
                error_msg = "\n".join(processing_errors)
                if not messagebox.askyesno("أخطاء في المعالجة",
                                         f"تم العثور على الأخطاء التالية:\n{error_msg}\n\nهل تريد المتابعة؟"):
                    self.progress_bar.stop()
                    self.update_status("تم إلغاء الاستيراد")
                    return

            # تحضير البيانات للإدراج في قاعدة البيانات
            records = self.data_processor.prepare_for_database(processed_df)

            # حذف البيانات القديمة (اختياري)
            if messagebox.askyesno("تأكيد", "هل تريد حذف البيانات الموجودة واستبدالها بالبيانات الجديدة؟"):
                self.db_manager.clear_all_students()

            # إدراج البيانات الجديدة
            success_count = 0
            for record in records:
                try:
                    self.db_manager.insert_student_data(record)
                    success_count += 1
                except Exception as e:
                    print(f"خطأ في إدراج السجل: {e}")

            # تحديث العرض
            self.load_data()

            self.progress_bar.stop()

            # رسالة النجاح مع التفاصيل
            success_msg = f"✅ تم استيراد {success_count} سجل بنجاح\n\n"
            success_msg += f"📊 إجمالي الصفوف: {len(df)}\n"
            success_msg += f"📋 إجمالي الأعمدة: {len(df.columns)}\n"

            if warnings:
                success_msg += f"⚠️ تحذيرات: {len(warnings)}\n"

            # إحصائيات البيانات المالية
            financial_cols = [col for col in df.columns if any(x in col for x in ['قسط', 'رسم', 'مصروفات', 'خصم', 'مديونية'])]
            if financial_cols:
                success_msg += f"💰 الأعمدة المالية: {len(financial_cols)}"

            messagebox.showinfo("نجح الاستيراد", success_msg)
            self.update_status(f"تم استيراد {success_count} سجل مع معالجة محسنة")

        except Exception as e:
            self.progress_bar.stop()
            messagebox.showerror("خطأ", f"خطأ في استيراد الملف: {str(e)}")
            self.update_status("خطأ في الاستيراد")

    def export_excel(self):
        """تصدير البيانات إلى Excel"""
        try:
            if self.filtered_data.empty:
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            self.update_status("جاري تصدير البيانات...")
            self.progress_bar.start()

            # تصدير الملف
            success, message = self.excel_handler.export_to_excel(self.filtered_data)

            self.progress_bar.stop()

            if success:
                messagebox.showinfo("نجح التصدير", message)
                self.update_status("تم التصدير بنجاح")
            else:
                messagebox.showerror("خطأ في التصدير", message)
                self.update_status("فشل في التصدير")

        except Exception as e:
            self.progress_bar.stop()
            messagebox.showerror("خطأ", f"خطأ في تصدير الملف: {str(e)}")
            self.update_status("خطأ في التصدير")

    def create_template(self):
        """إنشاء قالب Excel"""
        try:
            self.update_status("جاري إنشاء قالب Excel...")

            success, message = self.excel_handler.create_template_file()

            if success:
                messagebox.showinfo("نجح إنشاء القالب", message)
                self.update_status("تم إنشاء القالب بنجاح")
            else:
                messagebox.showerror("خطأ", message)
                self.update_status("فشل في إنشاء القالب")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء القالب: {str(e)}")
            self.update_status("خطأ في إنشاء القالب")

    def manage_fees(self):
        """إدارة الرسوم - استيراد من الواجهة العادية"""
        try:
            # استيراد دالة إدارة الرسوم من الواجهة العادية
            from gui_interface import FinanceGUI
            temp_gui = FinanceGUI()
            temp_gui.root.withdraw()  # إخفاء النافذة الرئيسية
            temp_gui.open_fee_management_window()
            temp_gui.root.destroy()  # تنظيف

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في فتح إدارة الرسوم: {str(e)}")

    def transfer_to_new_year(self):
        """نقل للسنة الجديدة - استيراد من الواجهة العادية"""
        try:
            # التحقق من وجود رسوم محددة للسنة الجديدة
            fee_structure = self.db_manager.get_fee_structure()
            if fee_structure.empty:
                messagebox.showwarning(
                    "رسوم غير محددة",
                    "يجب تحديد الرسوم الدراسية للسنة الجديدة أولاً.\n\nانقر على 'إدارة الرسوم' لتحديد الرسوم."
                )
                return

            # التحقق من وجود بيانات للنقل
            if self.current_data.empty:
                messagebox.showwarning(
                    "لا توجد بيانات",
                    "يجب استيراد بيانات السنة المالية القديمة أولاً.\n\nانقر على 'استيراد ملف Excel' لتحميل البيانات."
                )
                return

            # استيراد دالة النقل من الواجهة العادية
            from gui_interface import FinanceGUI
            temp_gui = FinanceGUI()
            temp_gui.current_data = self.current_data  # نقل البيانات
            temp_gui.root.withdraw()  # إخفاء النافذة الرئيسية
            temp_gui.transfer_to_new_year()

            # تحديث البيانات في الواجهة المحسنة
            self.load_data()
            temp_gui.root.destroy()  # تنظيف

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في نقل البيانات: {str(e)}")

    def show_statistics(self):
        """عرض الإحصائيات"""
        messagebox.showinfo("قريباً", "ميزة الإحصائيات المتقدمة ستكون متاحة قريباً")

    def analyze_data(self):
        """تحليل البيانات"""
        messagebox.showinfo("قريباً", "ميزة تحليل البيانات ستكون متاحة قريباً")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()

    def copy_filtered_results(self):
        """نسخ النتائج المفلترة"""
        messagebox.showinfo("قريباً", "ميزة نسخ النتائج ستكون متاحة قريباً")

    def export_filtered_data(self):
        """تصدير البيانات المفلترة"""
        messagebox.showinfo("قريباً", "ميزة تصدير البيانات المفلترة ستكون متاحة قريباً")

    def print_report(self):
        """طباعة التقرير"""
        messagebox.showinfo("قريباً", "ميزة طباعة التقرير ستكون متاحة قريباً")

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        messagebox.showinfo("قريباً", "ميزة النسخ الاحتياطي ستكون متاحة قريباً")

    def copy_selected(self):
        """نسخ المحدد"""
        messagebox.showinfo("قريباً", "ميزة النسخ ستكون متاحة قريباً")

    def edit_selected(self):
        """تحرير المحدد"""
        messagebox.showinfo("قريباً", "ميزة التحرير ستكون متاحة قريباً")

    def delete_selected(self):
        """حذف المحدد"""
        messagebox.showinfo("قريباً", "ميزة الحذف ستكون متاحة قريباً")

    def run(self):
        """
        تشغيل التطبيق
        """
        self.root.mainloop()

# دالة لتشغيل الواجهة المحسنة
def run_enhanced_gui():
    app = EnhancedFinanceGUI()
    app.run()

if __name__ == "__main__":
    run_enhanced_gui()
